package com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk;

import lombok.Data;
import org.mongodb.morphia.annotations.Property;

/**
 * 钉钉部门扩展数据 - MongoDB
 */
@Data
public class OutDeptData {
    /**
     * 钉钉部门ID
     */
    @Property("deptId")
    private Long deptId;

    /**
     * 钉钉父部门ID
     */
    @Property("parentId")
    private Long parentId;

    /**
     * 部门名称
     */
    @Property("name")
    private String name;

    /**
     * 子部门顺序
     */
    @Property("seq")
    private Integer seq;

    /**
     * 钉钉部门负责人
     */
    @Property("owner")
    private String owner;
}