package com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.isv;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 钉钉拒绝数据实体类 - PostgreSQL
 */
@Data
@TableName("dingtalk_isv_ding_refuse_data")
public class DingRefuseDataPgEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 业务数据
     */
    private String bizData;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 企业ei
     */
    private Integer ei;

    /**
     * 拒绝的应用ID
     */
    private Integer refuseAppId;

    /**
     * 拒绝的套件ID
     */
    private String refuseSuiteId;

    /**
     * 是否拦截的状态 1:拦截 0:不拦截
     */
    private Integer activeStatus;

    /**
     * 订单ID
     */
    private Long orderId;
}