package com.facishare.open.oa.base.dbproxy.mongo.document;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.*;
import lombok.experimental.Accessors;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.io.Serializable;

/**
 * oa连接器事件推送文档模板
 * <AUTHOR>
 * @date 2023/12/06
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class OaConnectorSyncEventDataDoc implements Serializable {
    /**
     * 唯一标识
     */
    @BsonId
    private ObjectId id;
    private ChannelEnum channel;
    private String outEa;
    private String appId;
    private String eventType;
    private String event;
    private Integer status;
    /**
     * 数据创建时间
     */
    private Long createTime;
    /**
     * 数据更新时间
     */
    private Long updateTime;
}
