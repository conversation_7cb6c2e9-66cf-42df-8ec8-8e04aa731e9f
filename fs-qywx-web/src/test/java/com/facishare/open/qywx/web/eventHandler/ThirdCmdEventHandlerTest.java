package com.facishare.open.qywx.web.eventHandler;

import com.facishare.open.qywx.web.model.qyweixin.QyweixinGetPermenantCodeRsp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class ThirdCmdEventHandlerTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private ThirdCmdEventHandler thirdCmdEventHandler;

    @Test
    public void testHandleCreateAuth() throws Exception {

        /**
         *         //TODO
         *         corpAuthInfoResult.setCode("0");
         *         String json = "{\"errcode\":0,\"errmsg\":null,\"access_token\":\"****7OvqTWZyNkYuMldeVW64kNLzh-8G1IwIRAq0eFcqklKZCgNdnYnCAsSywmuy...\",\"expires_in\":7200,\"permanent_code\":\"NPZ7mReQ2VgW70qes981LKe43OUQCmKqMtdluE-kaGo\",\"auth_corp_info\":{\"corpid\":\"wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA\",\"corp_name\":\"测试1201\",\"corp_type\":\"unverified\",\"corp_square_logo_url\":\"https://wework.qpic.cn/wwpic3az/979535_4zXHU2egQzim7T__1733123579/0\",\"corp_user_max\":1000,\"corp_agent_max\":0,\"corp_full_name\":null,\"verified_end_time\":null,\"subject_type\":1,\"corp_wxqrcode\":\"https://wework.qpic.cn/wwpic3az/452137_WKfg75oQRCi7Yp4_1742907624/0\",\"corp_scale\":\"1-50人\",\"corp_industry\":\"IT服务\",\"corp_sub_industry\":\"互联网和相关服务\",\"location\":\"\"},\"auth_info\":{\"agent\":[{\"agentid\":1000009,\"name\":\"112纷享销客CRM\",\"round_logo_url\":null,\"square_logo_url\":\"https://wework.qpic.cn/bizmail/RFQroF6XUh87UklTCyBIkvlFs4Hr7POkwMSzzQKb1ibbibE2XDqd6nibg/0\",\"auth_mode\":0,\"is_customized_app\":false,\"privilege\":{\"level\":1,\"allow_party\":[],\"allow_user\":[\"wowx1mDAAAswtw60xRomOCQexeLXdU3w\"],\"allow_tag\":[],\"extra_party\":[],\"extra_user\":[],\"extra_tag\":[]}}]},\"auth_user_info\":{\"userid\":\"wowx1mDAAAswtw60xRomOCQexeLXdU3w\",\"name\":\"wowx1mDAAAswtw60xRomOCQexeLXdU3w\",\"avatar\":\"https://rescdn.qqmail.com/node/wwmng/wwmng/style/images/independent/DefaultAvatar\\$73ba92b5.png\"},\"edition_info\":{\"agent\":[{\"agentid\":1000009,\"app_status\":2,\"edition_id\":\"sp4294bebe4b9a6ddc\",\"edition_name\":\"试用标准版\",\"user_limit\":4294967295,\"expired_time\":1734419814}]}}";
         *         QyweixinGetPermenantCodeRsp corpAuthInfo1 = new Gson().fromJson(json, new TypeToken<QyweixinGetPermenantCodeRsp>() {
         *         }.getType());
         *         corpAuthInfoResult.setData(corpAuthInfo1);
         */
//        String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><AuthCode><![CDATA[PBYW4zA8Nvu_uUvDZ3ZZopuuSn2XHP8y65OHifJTxvk0y89hMX31A5NnqmDinZdNRJr7WTnxkco5IUBawgea6YRcLZTCnnoNGpDR6q235ts]]></AuthCode><InfoType><![CDATA[create_auth]]></InfoType><TimeStamp>1742907622</TimeStamp></xml>";
//        String appId = "wx88a141937dd6f838";

//        corpAuthInfoResult.setCode("0");
//        String json = "{\"errcode\":0,\"errmsg\":null,\"access_token\":\"****B3O_zz_9ySczMhBkIMObOwQSowakIYulCN1kD7WPr7QHQIpfdz7gEyK2UobV...\",\"expires_in\":7200,\"permanent_code\":\"PFECZ1R_yplEbkyGmRLLVsYd5dkCtKLLUK0y53YNNYw\",\"auth_corp_info\":{\"corpid\":\"wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA\",\"corp_name\":\"测试1201\",\"corp_type\":\"unverified\",\"corp_square_logo_url\":\"https://wework.qpic.cn/wwpic3az/979535_4zXHU2egQzim7T__1733123579/0\",\"corp_user_max\":1000,\"corp_agent_max\":0,\"corp_full_name\":null,\"verified_end_time\":null,\"subject_type\":1,\"corp_wxqrcode\":\"https://wework.qpic.cn/wwpic3az/816918_R0veUFz5RqC9dCg_1743583910/0\",\"corp_scale\":\"1-50人\",\"corp_industry\":\"IT服务\",\"corp_sub_industry\":\"互联网和相关服务\",\"location\":\"\"},\"auth_info\":{\"agent\":[{\"agentid\":1000010,\"name\":\"112纷享订货管理\",\"round_logo_url\":null,\"square_logo_url\":\"https://wework.qpic.cn/wwpic/397863_D9BFt6IuTzWN8zI_1667801970/0\",\"auth_mode\":0,\"is_customized_app\":false,\"privilege\":{\"level\":1,\"allow_party\":[],\"allow_user\":[\"wowx1mDAAAswtw60xRomOCQexeLXdU3w\"],\"allow_tag\":[],\"extra_party\":[],\"extra_user\":[],\"extra_tag\":[]},\"shared_from\":null}]},\"auth_user_info\":{\"userid\":\"wowx1mDAAAswtw60xRomOCQexeLXdU3w\",\"name\":\"wowx1mDAAAswtw60xRomOCQexeLXdU3w\",\"avatar\":\"\"},\"edition_info\":{\"agent\":[{\"agentid\":1000010,\"app_status\":1,\"edition_id\":\"sp9d9ba17552a48e54\",\"edition_name\":\"试用版\",\"user_limit\":4294967295,\"expired_time\":1746175907}]}}";
//        QyweixinGetPermenantCodeRsp corpAuthInfo1 = new Gson().fromJson(json, new TypeToken<QyweixinGetPermenantCodeRsp>() {
//        }.getType());
//        corpAuthInfoResult.setData(corpAuthInfo1);

        String xml = "<xml><SuiteId><![CDATA[wx105357ca56c6db18]]></SuiteId><AuthCode><![CDATA[PBYW4zA8Nvu_uUvDZ3ZZooMrQ3cLEp3GJNta7vVYHRE_I-aEQD2TkB0rdYt6KCnpoiyNLASuxinm5g7Bt_Fb71hCo17fO7Jnm__2HL91sQc]]></AuthCode><InfoType><![CDATA[create_auth]]></InfoType><TimeStamp>1743583908</TimeStamp></xml>";
        String appId = "wx105357ca56c6db18";

        thirdCmdEventHandler.handle(xml, appId);
    }

    //openOrder
    @Test
    public void testHandleOpenOrder() throws Exception {
        //订单开通、新增
        String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><PaidCorpId><![CDATA[wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA]]></PaidCorpId><InfoType><![CDATA[open_order]]></InfoType><TimeStamp>1742972345</TimeStamp><OrderId><![CDATA[T00000C661DC367E3A5B9CA911BE2]]></OrderId><OperatorId><![CDATA[wowx1mDAAAswtw60xRomOCQexeLXdU3w]]></OperatorId></xml>";
        //订单续期，新增
        String xml1 = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><PaidCorpId><![CDATA[wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA]]></PaidCorpId><InfoType><![CDATA[open_order]]></InfoType><TimeStamp>1742990885</TimeStamp><OrderId><![CDATA[T00000C661DC367E3EE25CA911B25]]></OrderId><OperatorId><![CDATA[wowx1mDAAAswtw60xRomOCQexeLXdU3w]]></OperatorId></xml>";
        String appId = "wx88a141937dd6f838";
        thirdCmdEventHandler.handle(xml1, appId);
    }

    @Test
    public void testHandlePaidForSuccess() throws Exception {
        //支付成功
        String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><PaidCorpId><![CDATA[wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA]]></PaidCorpId><InfoType><![CDATA[pay_for_app_success]]></InfoType><TimeStamp>1742977651</TimeStamp><OrderId><![CDATA[T00000C661DC367E3A5B9CA911BE2]]></OrderId><OperatorId><![CDATA[wowx1mDAAAswtw60xRomOCQexeLXdU3w]]></OperatorId></xml>";
        //扩容
        String xml1 = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><PaidCorpId><![CDATA[wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA]]></PaidCorpId><InfoType><![CDATA[pay_for_app_success]]></InfoType><TimeStamp>1742987781</TimeStamp><OrderId><![CDATA[T00000C661DC367E3E204CA911B42]]></OrderId><OperatorId><![CDATA[wowx1mDAAAswtw60xRomOCQexeLXdU3w]]></OperatorId></xml>";
        //续期
        String xml2 = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><PaidCorpId><![CDATA[wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA]]></PaidCorpId><InfoType><![CDATA[pay_for_app_success]]></InfoType><TimeStamp>1742991350</TimeStamp><OrderId><![CDATA[T00000C661DC367E3EE25CA911B25]]></OrderId><OperatorId><![CDATA[wowx1mDAAAswtw60xRomOCQexeLXdU3w]]></OperatorId></xml>";
        String appId = "wx88a141937dd6f838";
        thirdCmdEventHandler.handle(xml2, appId);
    }

    @Test
    public void testHandleCancelAuth() throws Exception {
        String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><AuthCorpId><![CDATA[wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA]]></AuthCorpId><InfoType><![CDATA[cancel_auth]]></InfoType><TimeStamp>1742470081</TimeStamp></xml>";
        String appId = "dkdf3684b6720635f7";
        thirdCmdEventHandler.handle(xml, appId);
    }

    @Test
    public void testHandleChangeAuth() throws Exception {
        String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><InfoType><![CDATA[change_auth]]></InfoType><TimeStamp>1403610513</TimeStamp><AuthCorpId><![CDATA[wpwx1mDAAAOvtbJxYM8CScaAVqKJlC-Q]]></AuthCorpId><State><![CDATA[abc]]></State><ExtraInfo></ExtraInfo></xml>";
        String appId = "wx88a141937dd6f838";
        thirdCmdEventHandler.handle(xml, appId);
    }

    @Test
    public void testHandleChangeContact() throws Exception {
        //人员变更通知事件
//        String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><AuthCorpId><![CDATA[wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA]]></AuthCorpId><InfoType><![CDATA[change_contact]]></InfoType><TimeStamp>1744078556</TimeStamp><ChangeType><![CDATA[update_user]]></ChangeType><UserID><![CDATA[wowx1mDAAAswtw60xRomOCQexeLXdU3w]]></UserID><EnglishName><![CDATA[刘姗-华为鸿蒙智家/家庭绿电/影音]]></EnglishName><Alias><![CDATA[刘姗-华为鸿蒙智家/家庭绿电/影音]]></Alias><OpenUserID><![CDATA[wowx1mDAAAswtw60xRomOCQexeLXdU3w]]></OpenUserID></xml>";
        String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><AuthCorpId><![CDATA[wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA]]></AuthCorpId><InfoType><![CDATA[change_contact]]></InfoType><TimeStamp>1744078556</TimeStamp><ChangeType><![CDATA[delete_user]]></ChangeType><UserID><![CDATA[wowx1mDAAAWCwJqwbC7lR0xdRt_jgOFA]]></UserID><EnglishName><![CDATA[刘姗-华为鸿蒙智家/家庭绿电/影音]]></EnglishName><Alias><![CDATA[刘姗-华为鸿蒙智家/家庭绿电/影音]]></Alias><OpenUserID><![CDATA[wowx1mDAAAWCwJqwbC7lR0xdRt_jgOFA]]></OpenUserID></xml>";
        String appId = "wx88a141937dd6f838";
        thirdCmdEventHandler.handle(xml, appId);
    }

    @Test
    public void testChangeExternalContact() throws Exception {
        String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><AuthCorpId><![CDATA[wpwx1mDAAAOvtbJxYM8CScaAVqKJlC-Q]]></AuthCorpId><InfoType><![CDATA[change_external_contact]]></InfoType><TimeStamp>1744292090</TimeStamp><ChangeType><![CDATA[add_external_contact]]></ChangeType><UserID><![CDATA[wowx1mDAAADaR4zU0tv72vm-358jDT1w]]></UserID><ExternalUserID><![CDATA[wmwx1mDAAAULGU7ppBrfLmfn8dqwt7Ww]]></ExternalUserID><WelcomeCode><![CDATA[hCS3h0TzG8eLpK8ELVjAeZ4BL42LtZO1Lu9PNMdUGww]]></WelcomeCode></xml>";
//        String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><AuthCorpId><![CDATA[wpwx1mDAAAOvtbJxYM8CScaAVqKJlC-Q]]></AuthCorpId><InfoType><![CDATA[change_external_contact]]></InfoType><TimeStamp>1744292090</TimeStamp><ChangeType><![CDATA[del_external_contact]]></ChangeType><UserID><![CDATA[wowx1mDAAADaR4zU0tv72vm-358jDT1w]]></UserID><ExternalUserID><![CDATA[wmwx1mDAAAULGU7ppBrfLmfn8dqwt7Ww]]></ExternalUserID><WelcomeCode><![CDATA[hCS3h0TzG8eLpK8ELVjAeZ4BL42LtZO1Lu9PNMdUGww]]></WelcomeCode></xml>";
        String appId = "wx88a141937dd6f838";
        thirdCmdEventHandler.handle(xml, appId);
    }

    @Test
    public void testChangeExternalChat() throws Exception {
        String xml = "<xml><SuiteId><![CDATA[wx88a141937dd6f838]]></SuiteId><AuthCorpId><![CDATA[wpwx1mDAAAOvtbJxYM8CScaAVqKJlC-Q]]></AuthCorpId><InfoType><![CDATA[change_external_chat]]></InfoType><TimeStamp>1744352787</TimeStamp><ChatId><![CDATA[wrwx1mDAAAVJFlgZ4zTz4B19VWNi7dMg]]></ChatId><ChangeType><![CDATA[create]]></ChangeType></xml>";
        String appId = "wx88a141937dd6f838";
        thirdCmdEventHandler.handle(xml, appId);
    }
}
