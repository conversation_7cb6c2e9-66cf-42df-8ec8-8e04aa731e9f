package com.facishare.open.qywx.accountinner.result;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@AllArgsConstructor
public enum ResultCodeEnum {
    /**
     * 成功
     **/
    SUCCESS("0", "成功"),
    SYS_EXCEPTION("1000", "系统异常"),
    MISSING_PARAMETER("1010", "缺少参数"),
    APP_NOT_BIND("1020", "应用未安装或者已停用"),
    EXTERNAL_FOLLOW_USER_EXCEPTION("1030", "该员工不是该客户的跟进人"),
    REP_ACCESS_TOKEN_ERROR("1040", "代开发应用授权失败"),
    ;

    /**
     * 错误码
     */
    private final String code;
    /**
     * 错误信息
     */
    private final String msg;
}
