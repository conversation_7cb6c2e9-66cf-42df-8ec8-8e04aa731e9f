package com.facishare.open.qywx.accountbind.result;

/**
 * Created by fengyh on 2018/8/1.
 */
@Deprecated
public enum ErrorRefer {
    SUCC("s120050000"),// "成功",
    INTERNAL_ERROR("s320050001"), // "内部错误"
    QUERRY_EMPTY("s320050002"), // "查询数据为空",
    CORP_ACCOUNT_NOT_BIND("s320050003"), // "企业账号未绑定,请联系管理员",  企业正在初始化
    INVALID_TICKET("s320050004"), //找不到此ticket的信息
    EXPIRED_TICKET("s320050005"), //ticket已经过期
    CORP_NOT_AUTHORIZED("s320050006"), // "查不到企业的授权信息",
    BIND_ERROR("s320050007"), // "绑定数量为0",
    CORP_CREATING("s320050008"), //企业初始化中
    PARAM_ERROR("s320050009"), //参数错误
    INVALID_CALL("s320050011") //非法调用
    ; //"有异常产生";

    private String code;        //返回码
    private String msg;
    private String i18nKey;
    public String getCode() {
        return code;
    }
    ErrorRefer(String code){
        this.code = code;
    }
}

