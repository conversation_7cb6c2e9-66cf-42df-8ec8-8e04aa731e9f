package com.facishare.open.feishu.sync.test.mongo;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.oa.base.dbproxy.mongo.dao.OaConnectorOutDepartmentInfoMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorOutDepartmentInfoDoc;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.mongodb.client.result.DeleteResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.LinkedList;
import java.util.List;

public class OaConnectorOutDepartmentInfoMongoDaoTest extends BaseTest {
    @Autowired
    private OaConnectorOutDepartmentInfoMongoDao oaConnectorOutDepartmentInfoMongoDao;

    @Test
    public void batchReplace() {

    }

    @Test
    public void deleteDepartmentInfoByUserId() {
        DeleteResult deleteResult = oaConnectorOutDepartmentInfoMongoDao.deleteDepartmentInfoByUserId(ChannelEnum.feishu,
                "100d7a94d39e575e",
                null,
                "test1");
        deleteResult = oaConnectorOutDepartmentInfoMongoDao.deleteDepartmentInfoByUserId(ChannelEnum.feishu,
                "100d7a94d39e575e",
                null,
                "test2");
        System.out.println(deleteResult);
    }

    @Test
    public void queryDepartmentInfos() {
        List<OaConnectorOutDepartmentInfoDoc> docs = oaConnectorOutDepartmentInfoMongoDao.queryDepartmentInfos(ChannelEnum.feishu, "100d7a94d39e575e", null);
        System.out.println(docs);
    }

    @Test
    public void deleteNotInCollectionDocs() {
        List<OaConnectorOutDepartmentInfoDoc> docs = new LinkedList<>();
//        OaConnectorOutDepartmentInfoDoc doc = new OaConnectorOutDepartmentInfoDoc();
//        doc.setChannel(ChannelEnum.feishu);
//        doc.setCreateTime(System.currentTimeMillis());
//        doc.setUpdateTime(System.currentTimeMillis());
//        doc.setOutEa("100d08b69448975d");
//        doc.setOutDepartmentId("ou_aa822be0b3d68a3340d7bdc16ebb8b34");
//        doc.setOutDepartmentInfo("{xxxxxx}");
//        docs.add(doc);
        DeleteResult deleteResult = oaConnectorOutDepartmentInfoMongoDao.deleteNotInCollectionDocs(ChannelEnum.feishu, "100d08b69448975d", null, docs);
        System.out.println(deleteResult);
    }

    @Test
    public void countDocuments() {
        Long counts = oaConnectorOutDepartmentInfoMongoDao.countDocuments(ChannelEnum.feishu, "100d7a94d39e575e", null);
        System.out.println(counts);
    }
}
