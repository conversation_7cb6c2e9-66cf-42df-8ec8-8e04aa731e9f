package com.facishare.open.feishu.sync.service


import com.facishare.open.feishu.syncapi.arg.InsertEmployeeBindArg
import com.facishare.open.feishu.syncapi.arg.QueryEmployeeBindArg
import com.facishare.open.feishu.syncapi.arg.UpdateEmployeeBindArg

import com.facishare.open.feishu.syncapi.model.ContactScopeModel
import com.facishare.open.feishu.syncapi.result.data.UserData
import com.facishare.open.feishu.syncapi.service.EmployeeBindService
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy
import com.facishare.organization.api.model.employee.EmployeeDto
import com.google.common.collect.Lists
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class EmployeeBindServiceTest extends Specification {
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;

    def "autoBindEmpByFeishuEmployeeNumber"() {
        expect:
        ContactScopeModel contactScopeModel = new ContactScopeModel()
        List<UserData.User> userList = new LinkedList<>()
        UserData.User user = new UserData.User()
        user.setName("小杨11")
        user.setEmployee_no("jobnumber001")
        user.setOpenId("ou_b68ae376ff177b178e39d31d770195de")
        userList.add(user)
        contactScopeModel.setUserList(userList)
        def a = employeeBindService.autoBindEmpByFeishuEmployeeNumber("100d08b69448975d", "85903", contactScopeModel)
        print(a)
    }

    def "fsEmployeeInfoCache"() {
        expect:
        com.facishare.open.order.contacts.proxy.api.result.Result<List<EmployeeDto>> listResult = fsEmployeeServiceProxy.batchGetEmployeeDto(85903, Lists.newArrayList(1006));
        EmployeeDto employeeDto = listResult.getData().get(0);
        def a = employeeBindService.fsEmployeeInfoCache("85903", employeeDto)
        print(a)
    }

    def "queryEmployeeBind"() {
        given:
        QueryEmployeeBindArg arg = new QueryEmployeeBindArg()
        arg.setFsEa("85903")
        arg.setOutEa("100d08b69448975d")
        arg.setOutUserId(outUserIdCase)
        arg.setFsUserId(fsUserIdCase)
        expect:
        def a = employeeBindService.queryEmployeeBind("feishu", arg)
        print(a)
        where:
        fsUserIdCase  |  outUserIdCase  || result
           "1001"     |      null        || null
           null       |  "ou_0fd979c697c5dd375d12ffb999492a91"  || null
    }

    def "insertEmployeeBind"() {
        given:
        InsertEmployeeBindArg arg = new InsertEmployeeBindArg()
        arg.setFsEa("85903")
        arg.setOutEa("100d08b69448975d")
        arg.setOutUserId("ou_0fd979c697c5dd375d12ffb999492a911")
        arg.setFsUserId("1111")
        expect:
        def a = employeeBindService.insertEmployeeBind("feishu", arg)
        print(a)
    }

    def "updateEmployeeBind"() {
        given:
        UpdateEmployeeBindArg arg = new UpdateEmployeeBindArg()
        arg.setFsEa("85903")
        arg.setOutEa("100d08b69448975d")
        arg.setOutUserId("ou_0fd979c697c5dd375d12ffb999492a9111")
        arg.setFsUserId("1122")
        expect:
        def a = employeeBindService.updateEmployeeBind("feishu", arg)
        print(a)
    }

    def "createEmployeeInfo"() {
        given:
        String outEa = outEaCase
        String appId = appIdCase
        String outUserId = outUserIdCase
        String fsEa = fsEaCase
        expect:
        def a = employeeBindService.createEmployeeInfo(outEa, appId, outUserId, fsEa)
        print(a)
        where:
        outEaCase  |  appIdCase  |  outUserIdCase  |  fsEaCase
        "16bdc45070d5975f"  |  "cli_a3ddeb52763b100c"  |  "ou_05bd252b1b30998da7d9027fc1473f6d"  |  "90429"
    }

    def "autoBindEmpByCrmEmployeeNumber"() {
        expect:
        def a = employeeBindService.autoBindEmpByCrmEmployeeNumber("85903", new LinkedList<>(),null)
        print(a)
    }

    def "queryEmployeeBindListByOutData"() {
        expect:
        def a = employeeBindService.queryEmployeeBindListByOutData(ChannelEnum.feishu, "16bdc45070d5975f", "ou_ee510be38a602ebc7a60f70a12308a3f")
        print(a)
    }
}
