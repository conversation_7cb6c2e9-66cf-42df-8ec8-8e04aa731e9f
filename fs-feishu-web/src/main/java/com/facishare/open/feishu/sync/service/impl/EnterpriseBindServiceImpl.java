package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.JSONException;
import com.facishare.converter.EIEAConverter;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.syncapi.consts.Constant;
import com.facishare.open.feishu.syncapi.model.connect.FeishuEnterpriseConnectParams;
import com.facishare.open.feishu.sync.entity.ErpConnectInfoEntity;
import com.facishare.open.feishu.sync.manager.*;
import com.facishare.open.feishu.sync.mq.sender.MQSyncSender;
import com.facishare.open.feishu.sync.threadpool.ThreadPoolHelper;
import com.facishare.open.feishu.sync.utils.IdGenerator;
import com.facishare.open.feishu.syncapi.arg.*;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaOrderInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.admin.FeiShuConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.feishu.syncapi.entity.*;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaOrderInfoEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.model.EnterpriseModel;
import com.facishare.open.feishu.syncapi.proto.OaconnectorEventDateChangeProto;
import com.facishare.open.feishu.syncapi.model.FeiShuConnectParam;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.QueryTenantInfoData;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.feishu.syncapi.service.FeishuTenantService;
import com.facishare.open.feishu.syncapi.service.OrderService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.facishare.paas.license.pojo.ModuleFlag;
import com.facishare.uc.api.model.enterprise.arg.BatchGetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service("enterpriseBindService")
public class EnterpriseBindServiceImpl implements EnterpriseBindService {
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private DepartmentBindManager departmentBindManager;
    @Resource
    private EmployeeBindManager employeeBindManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Resource
    private CorpInfoManager corpInfoManager;
    @Resource
    private ConnectInfoManager connectInfoManager;
    @Resource
    private FeishuTenantService feishuTenantService;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Resource
    private MQSyncSender mqSyncSender;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private ErpdssManager erpdssManager;
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private IdGenerator idGenerator;
    @Resource
    private OrderService orderService;
    @Resource
    private OrderInfoManager orderInfoManager;
    @Autowired
    private OuterOaOrderInfoManager outerOaOrderInfoManager;
    @Resource
    private FsOrderManager fsOrderManager;
    @Resource
    private I18NStringManager i18NStringManager;


    @Override
    public Result<List<OuterOaEnterpriseBindEntity>> getEnterpriseBindList(String outEa,String appId,String dataCenterId) {
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams=OuterOaEnterpriseBindParams.builder().outEa(outEa).appId(appId).id(dataCenterId).build();
        return Result.newSuccess(outerOaEnterpriseBindManager.getEntities(outerOaEnterpriseBindParams));
    }

    @Override
    public Result<List<EnterpriseModel>> getFsEaList(String outEa, String outUserId) {
        List<String> eaList;
        //所有企业都在当前环境，这个可以通过人员绑定关系查询
        OuterOaEmployeeBindParams outerOaEmployeeBindParams=OuterOaEmployeeBindParams.builder().outEa(outEa).outEmpId(outUserId).build();
        List<OuterOaEmployeeBindEntity> entityListByOutData = outerOaEmployeeBindManager.getEntities(outerOaEmployeeBindParams);
        if(CollectionUtils.isEmpty(entityListByOutData)) {
            return Result.newSuccess();
        }
        eaList = entityListByOutData.stream().map(OuterOaEmployeeBindEntity::getFsEa).distinct().collect(Collectors.toList());
        LogUtils.info("EnterpriseBindServiceImpl.getFsEaList,eaList={}", eaList);

        return Result.newSuccess(batchGetEnterpriseData(eaList));
    }

    @Override
    public Result<CorpInfoEntity> fsBindWithFeishu(FsBindWithFeishuArg arg) {
        String fsEa = arg.getFsEa();
        String displayId = arg.getDisplayId();
        String dataCenterName = arg.getDataCenterName();

        //1.查询企业信息表，确认飞书企业是否安装CRM应用
        CorpInfoEntity entity = corpInfoManager.getEntityByDisplayId(displayId);
        if(entity==null) {
            return Result.newError(ResultCodeEnum.CRM_APP_NOT_INSTALLED);
        }
        arg.setOutEa(entity.getTenantKey());

        EnterpriseBindEntity enterpriseBind = enterpriseBindManager.getEntityByOutEa(arg.getOutEa(),
                null,
                BindTypeEnum.auto);
        if(enterpriseBind!=null) {
            String msg = ResultCodeEnum.OUT_EA_IS_AUTO_BIND_CANNOT_MANUAL_BIND.getMsg() + ";fsEa=" + enterpriseBind.getFsEa();
            return Result.newError(ResultCodeEnum.OUT_EA_IS_AUTO_BIND_CANNOT_MANUAL_BIND.getCode(),msg);
        }

        GetDcBindArg getDcBindArg = new GetDcBindArg();
        getDcBindArg.setFsEa(arg.getFsEa());
        getDcBindArg.setOutEa(arg.getOutEa());
        getDcBindArg.setChannel("CONNECTOR_FEISHU");
        //2.获取飞书连接器信息
        Result<ErpConnectInfoEntity> dcBind = erpdssManager.getDcBind(getDcBindArg);
        if(dcBind!=null
                && dcBind.getData()!=null
                && !StringUtils.equalsIgnoreCase(dcBind.getData().getId(),arg.getDataCenterId())) {
            return Result.newError(ResultCodeEnum.ONE_FS_EA_CANNOT_BIND_SAME_OUT_EA);
        }

        int tenantId = eieaConverter.enterpriseAccountToId(fsEa);
        UpdateConnectParamsArg updateConnectParamsArg = new UpdateConnectParamsArg();
        updateConnectParamsArg.setTenantId(tenantId+"");
        updateConnectParamsArg.setDataCenterId(arg.getDataCenterId());
        updateConnectParamsArg.setDataCenterName(dataCenterName);
        updateConnectParamsArg.setConnectParams(JSONObject.toJSONString(arg));


        //3.判断连接信息是否存在，存在则更新
        ConnectInfoEntity connectInfoEntity = connectInfoManager.getEntity(fsEa, displayId);
        if(connectInfoEntity!=null) {
            connectInfoEntity.setConnectorName(dataCenterName);
            connectInfoManager.updateById(connectInfoEntity);
            erpdssManager.updateConnectParams(updateConnectParamsArg);
            return Result.newSuccess(entity);
        }

        //4.查询企业绑定表，确认纷享企业是否已经和飞书企业绑定
        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindManager.getEntity(fsEa, entity.getTenantKey());
        if(enterpriseBindEntity!=null && enterpriseBindEntity.getBindStatus()==BindStatusEnum.normal) {
            return Result.newError(ResultCodeEnum.FS_EA_HAS_BOUND);
        }

        int count = connectInfoManager.insert(fsEa,dataCenterName,displayId);
        if(count==1) {
            if(enterpriseBindEntity==null) {
                //5.保存纷享企业和飞书企业的绑定关系
                count = enterpriseBindManager.insert(ChannelEnum.feishu,
                        fsEa,
                        entity.getTenantKey(),
                        ConfigCenter.crm_domain,
                        BindTypeEnum.manual,
                        BindStatusEnum.normal);
                if(!ConfigCenter.MAIN_ENV) {
                    OaconnectorEventDateChangeProto proto = new OaconnectorEventDateChangeProto();
                    proto.setOutEa(entity.getTenantKey());
                    proto.setEventType("oaconnector_enterprise_bind");
                    proto.setType("add");
                    proto.setFsEa(fsEa);
                    proto.setDomain(ConfigCenter.crm_domain);
                    mqSyncSender.sendOaconnectorEventDataChangeMQ(ChannelEnum.feishu.name(), proto, String.valueOf(eieaConverter.enterpriseAccountToId(fsEa)));
                }
            } else {
                //5.绑定关系已存在，更新绑定状态为正常
                count = enterpriseBindManager.updateBindStatus(fsEa,entity.getTenantKey(),BindStatusEnum.normal);
                if(!ConfigCenter.MAIN_ENV) {
                    OaconnectorEventDateChangeProto proto = new OaconnectorEventDateChangeProto();
                    proto.setOutEa(entity.getTenantKey());
                    proto.setEventType("oaconnector_enterprise_bind");
                    proto.setType("update");
                    proto.setFsEa(fsEa);
                    proto.setDomain(ConfigCenter.crm_domain);
                    mqSyncSender.sendOaconnectorEventDataChangeMQ(ChannelEnum.feishu.name(), proto, String.valueOf(eieaConverter.enterpriseAccountToId(fsEa)));
                }
            }
            if(count==1) {
                erpdssManager.updateConnectParams(updateConnectParamsArg);
                return Result.newSuccess(entity);
            }
        }
        return Result.newError(ResultCodeEnum.SAVE_CORP_BIND_FAILED);
    }

    @Override
    public Result<Void> fsUnBindWithFeishu(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity) {
        //1.判断连接信息是否存在
        outerOaEnterpriseBindEntity.setBindStatus(BindStatusEnum.create);
        outerOaEnterpriseBindEntity.setUpdateTime(System.currentTimeMillis());
        FeiShuConnectorVo feiShuConnectorVo = JSONObject.parseObject(outerOaEnterpriseBindEntity.getConnectInfo(), FeiShuConnectorVo.class);
        BaseConnectorVo baseConnectorVo=new BaseConnectorVo();
        baseConnectorVo.setConnectorName(feiShuConnectorVo.getConnectorName());
        baseConnectorVo.setDataCenterName(feiShuConnectorVo.getDataCenterName());
        baseConnectorVo.setChannel(feiShuConnectorVo.getChannel());
        baseConnectorVo.setDataCenterId(feiShuConnectorVo.getDataCenterId());
        outerOaEnterpriseBindEntity.setConnectInfo(JSONObject.toJSONString(baseConnectorVo));
        outerOaEnterpriseBindEntity.setOutEa(null);
        outerOaEnterpriseBindEntity.setAppId(null);
        //2.查询企业绑定表，确认纷享企业是否已经和飞书企业绑定
        outerOaEnterpriseBindManager.batchUpsertById(Lists.newArrayList(outerOaEnterpriseBindEntity));

        //先不做删除部门人员绑定关系
        return Result.newSuccess();
    }

    @Override
    public Result<Void> fsUnBindWithFeishu2(String displayId) {
//        CorpInfoEntity entity = corpInfoManager.getEntityByDisplayId(displayId);
//        if(entity==null) {
//            return Result.newError(ResultCodeEnum.CRM_APP_NOT_INSTALLED);
//        }
//
//        OuterOaEnterpriseBindEntity enterpriseBind = enterpriseBindManager.getEntityByOutEa(entity.getTenantKey(),
//                null,
//                BindTypeEnum.auto);
//        log.info("fsUnBindWithFeishu2,enterpriseBind={}",enterpriseBind);
//        if(enterpriseBind!=null) {
//            return fsUnBindWithFeishu(enterpriseBind.getFsEa(),displayId,null);
//        }
        return Result.newError(ResultCodeEnum.UNBIND_FAILED_WITH_AUTO_BIND_EA);
    }

    @Override
    public Result<Void> feishuUnBindWithFs(String outEa) {

        //2.查询企业绑定表，确认飞书企业是否已经和纷享企业绑定
        List<OuterOaEnterpriseBindEntity> entitiesByFsEaChanelEnums = outerOaEnterpriseBindManager.getEntitiesNormalByChanelEnums(Lists.newArrayList(ChannelEnum.feishu, ChannelEnum.lark),null, outEa);

        if(CollectionUtils.isEmpty(entitiesByFsEaChanelEnums)) {
            return Result.newError(ResultCodeEnum.OUT_EA_NOT_BIND);
        }
        OuterOaEnterpriseBindEntity enterpriseBindEntity = entitiesByFsEaChanelEnums.get(0);
        String fsEa = enterpriseBindEntity.getFsEa();

        //3.删除纷享企业和飞书企业的绑定关系
        enterpriseBindManager.deleteByFsEa(fsEa,outEa);
        if(!ConfigCenter.MAIN_ENV) {
            OaconnectorEventDateChangeProto proto = new OaconnectorEventDateChangeProto();
            proto.setOutEa(outEa);
            proto.setEventType("oaconnector_enterprise_bind");
            proto.setType("deleteByFsEa");
            proto.setFsEa(fsEa);
            proto.setDomain(ConfigCenter.crm_domain);
            mqSyncSender.sendOaconnectorEventDataChangeMQ(ChannelEnum.feishu.name(), proto, String.valueOf(eieaConverter.enterpriseAccountToId(fsEa)));
        }
        //4.删除纷享企业和飞书部门的绑定关系
        departmentBindManager.deleteByFsEa(fsEa,outEa);
        //5.删除纷享企业和飞书人员的绑定关系
        employeeBindManager.deleteByFsEa(fsEa,outEa);
        return Result.newSuccess();
    }
    @Deprecated
    @Override
    public Result<FeiShuConnectParam> queryConnectInfo(QueryConnectInfoArg arg) {
        log.info("EnterpriseBindServiceImpl.queryConnectInfo,arg={}",arg);
        String displayId = null;
        String tenantId = eieaConverter.enterpriseAccountToId(arg.getFsEa()) + "";
        String lang = TraceUtils.getLocale();
        String dataCenterName = i18NStringManager.get(I18NStringEnum.s100, lang, tenantId);
        Result<ErpConnectInfoEntity> dcInfo = erpdssManager.getDcInfo(arg.getFsEa(), arg.getDataCenterId());
        log.info("EnterpriseBindServiceImpl.queryConnectInfo,dcInfo={}",dcInfo);
        if(dcInfo.isSuccess()) {
            if(dcInfo.getData()!=null) {
                if(dcInfo.getData().getNumber()!=1500) {
                    if((StringUtils.isEmpty(dcInfo.getData().getConnectParams())
                            || StringUtils.equalsIgnoreCase(dcInfo.getData().getConnectParams(),"{}"))) {
                        FeiShuConnectParam model = FeiShuConnectParam.builder()
                                .dataCenterId(arg.getDataCenterId())
                                .dataCenterName(dcInfo.getData().getDataCenterName())
                                .fsEa(arg.getFsEa())
                                .build();
                        return Result.newSuccess(model);
                    }
                }
                if(StringUtils.isNotEmpty(dcInfo.getData().getConnectParams())) {
                    FeiShuConnectParam connectParam = FeiShuConnectParam.parse2(dcInfo.getData().getConnectParams());
                    if(StringUtils.isNotEmpty(connectParam.getDisplayId())) {
                        displayId = connectParam.getDisplayId();
                    }
                    if(StringUtils.isNotEmpty(connectParam.getDataCenterName())) {
                        dataCenterName = connectParam.getDataCenterName();
                    }
                }
            }
        }

        if(StringUtils.isEmpty(displayId)) {
            //1.查询连接信息
            ConnectInfoEntity connectInfoEntity = connectInfoManager.getEntity(arg.getFsEa(),displayId);
            log.info("EnterpriseBindServiceImpl.queryConnectInfo,connectInfoEntity={}",connectInfoEntity);
            if(connectInfoEntity==null) {
                FeiShuConnectParam model = FeiShuConnectParam.builder()
                        .dataCenterId(arg.getDataCenterId())
                        .dataCenterName(dataCenterName)
                        .fsEa(arg.getFsEa())
                        .build();
                return Result.newSuccess(model);
            }
            displayId = connectInfoEntity.getDisplayId();
            dataCenterName = connectInfoEntity.getConnectorName();
        }

        CorpInfoEntity corpInfoEntity = corpInfoManager.getEntityByDisplayId(displayId);
        String outEa = corpInfoEntity.getTenantKey();
        //2.查询企业绑定信息
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(arg.getFsEa(),outEa);
        log.info("EnterpriseBindServiceImpl.queryConnectInfo,entity={}",entity);
        if(entity==null) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        //3.查询飞书租户信息
        Result<QueryTenantInfoData> result = feishuTenantService.queryTenantInfo(ConfigCenter.feishuCrmAppId, entity.getOutEa());
        log.info("EnterpriseBindServiceImpl.queryConnectInfo,queryTenantInfo,result={}",result);
        String outEn = "";
        if(result.isSuccess() && result.getData()!=null) {
            outEn = result.getData().getTenant().getName();
        }
        //4.组装返回结果
        FeiShuConnectParam model = FeiShuConnectParam.builder()
                .dataCenterId(arg.getDataCenterId())
                .dataCenterName(dataCenterName)
                .fsEa(entity.getFsEa())
                .outEa(entity.getOutEa())
                .outEn(outEn)
                .displayId(displayId)
                .bindStatus(entity.getBindStatus())
                .bindType(entity.getBindType())
                .build();
        log.info("EnterpriseBindServiceImpl.queryConnectInfo,model={}",model);
        return Result.newSuccess(model);
    }

    @Override
    public Result<String> queryScanCodeAuthUrl(String fsEa, String dataCenterId) {
        //只有isv.先不处理其他appid
        String state = fsEa + "_" + dataCenterId;
        String lang = TraceUtils.getLocale();
        String scanCodeAuthUrl = "https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri={redirect_uri}&app_id={app_id}&state="+state;
        scanCodeAuthUrl = scanCodeAuthUrl.replace("{app_id}",ConfigCenter.feishuCrmAppId)
                .replace("{redirect_uri}", URLEncoder.encode(ConfigCenter.feishu_scan_code_auth_redirect_url + "?fsEa=" + fsEa + "&lang=" + lang));
        return new Result<>(scanCodeAuthUrl);
    }

    @Override
    public Result<Boolean> hasManualBind(String outEa) {

        return Result.newSuccess();
    }

    private List<EnterpriseModel> batchGetEnterpriseData(List<String> eaList) {
        BatchGetEnterpriseDataArg arg = new BatchGetEnterpriseDataArg();
        arg.setEnterpriseAccounts(eaList);
        BatchGetEnterpriseDataResult result = enterpriseEditionService.batchGetEnterpriseData(arg);
        List<EnterpriseModel> modelList = new ArrayList<>();
        if(ObjectUtils.isEmpty(result) || CollectionUtils.isEmpty(result.getEnterpriseDatas())) {
            return modelList;
        }
        for(EnterpriseData data : result.getEnterpriseDatas()) {
            EnterpriseModel model = new EnterpriseModel();
            model.setEa(data.getEnterpriseAccount());
            model.setName(data.getEnterpriseName());
            modelList.add(model);
        }

        return modelList;
    }

    @Override
    public Result<Void> updateEnterpriseExtend(String fsEa, String outEa, String extendField, Object extendValue) {
        //通过fsEa查询企业绑定关系
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.feishu,fsEa, outEa,null);
        log.info("EnterpriseBindServiceImpl.updateEnterpriseExtend,enterpriseBindEntity={}",enterpriseBindEntity);
        if(enterpriseBindEntity==null) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        //获取字段
        FeiShuConnectorVo connectParams = new Gson().fromJson(StringUtils.isNotEmpty(enterpriseBindEntity.getConnectInfo()) ? enterpriseBindEntity.getConnectInfo() : GlobalValue.enterprise_extend, FeiShuConnectorVo.class);

        //更新字段，要先判断字段是否符合
        switch(extendField) {
            case "isFirstLand":
                if(StringUtils.equalsIgnoreCase(extendValue.toString(), Boolean.FALSE.toString())
                        || StringUtils.equalsIgnoreCase(extendValue.toString(), Boolean.TRUE.toString())) {
                    connectParams.setIsFirstLand(Boolean.valueOf(extendValue.toString()));
                }
                break;
            case "isRetainInformation":
                if(StringUtils.equalsIgnoreCase(extendValue.toString(), Boolean.FALSE.toString())
                        || StringUtils.equalsIgnoreCase(extendValue.toString(), Boolean.TRUE.toString())) {
                    connectParams.setIsRetainInformation(Boolean.valueOf(extendValue.toString()));
                }
                break;
            default:
                log.info("EnterpriseBindServiceImpl.updateEnterpriseExtend,This field is not supported,extendField={}",extendField);
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        enterpriseBindEntity.setConnectInfo(new Gson().toJson(connectParams));
        //保存数据
        enterpriseBindManager.updateExtend(fsEa, enterpriseBindEntity.getOutEa(), enterpriseBindEntity);
        return Result.newSuccess();
    }

    @Override
    public Result<List<OuterOaEnterpriseBindEntity>> queryEnterprisesByBindType(BindTypeEnum bindType) {
        return Result.newSuccess(enterpriseBindManager.getEnterpriseBindListByBindType(bindType));
    }

    @Override
    public Result<Integer> saveAutoBind(String fsEa, String outEa, Integer autoBind,ChannelEnum channelEnum) {

        List<OuterOaEnterpriseBindEntity> entitiesByFsOuterEA = outerOaEnterpriseBindManager.getEntitiesByFsOuterEA(ChannelEnum.feishu, fsEa, outEa);
        if(ObjectUtils.isEmpty(entitiesByFsOuterEA)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        OuterOaEnterpriseBindEntity entity = entitiesByFsOuterEA.get(0);
        FeishuEnterpriseConnectParams feishuEnterpriseConnectParams = Optional.ofNullable(entity.getConnectInfo())
                .filter(StringUtils::isNotBlank)
                .map(json -> {
                    try {
                        return JSONObject.parseObject(json, FeishuEnterpriseConnectParams.class);
                    } catch (JSONException e) {
                        log.warn("Invalid connect info format", e);
                        return null;
                    }
                })
                .orElseGet(FeishuEnterpriseConnectParams::new);
        feishuEnterpriseConnectParams.setIsAutoBind(autoBind);
        String autoField = Constant.AUTO_BIND_FIELD_EMPNUM;
        if(ChannelEnum.lark.name().equals(channelEnum.name())){
            FeishuEnterpriseConnectParams connectParam = ChannelEnum.lark.getConnectParam(entity.getConnectInfo());
            autoField=Constant.AUTO_BIND_FIELD_EMAIL;
            connectParam.setAutoField(autoField);
            entity.setConnectInfo(JSONObject.toJSONString(connectParam));
        }
        Integer update =outerOaEnterpriseBindManager.updateById(entity);
        if(update > 0 && autoBind == 2) {
            String finalAutoField = autoField;
            ThreadPoolHelper.autoBindThreadPool.submit(()->{
                employeeBindService.autoBindEmpByCrmEmployeeNumber(fsEa, outEa, new LinkedList<>(), finalAutoField);
            });
        }
        return Result.newSuccess(update);
    }

    @Override
    public Result<Integer> queryAutoBind(String fsEa, String outEa) {
        OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.feishu,fsEa, outEa,null);
        if(ObjectUtils.isEmpty(entity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        FeishuEnterpriseConnectParams connectParam = ChannelEnum.feishu.getConnectParam(entity.getConnectInfo());
        return Result.newSuccess(connectParam.getIsAutoBind());
    }

    @Override
    public Result<Void> checkAndInitConnector(String fsEa, String dataCenterId,String channel) {
        String tenantId = eieaConverter.enterpriseAccountToId(fsEa)+"";
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
        if(ObjectUtils.isEmpty(entityById)){
            log.info("checkout init connect null:{}",fsEa);
            return Result.newSuccess();
        }

        //1.检查企业是否是自动绑定的企业
        if(entityById.getBindType().equals(BindTypeEnum.manual)){
            log.info("checkAndInitConnector,manual bind,feishu connector exist, return only");
            return Result.newSuccess();
        }
        //3.购买飞书连接器订单

        OuterOaOrderInfoEntity lastOrder = outerOaOrderInfoManager.getLatestOrder(entityById.getOutEa());
        log.info("checkAndInitConnector,latestOrder={}",lastOrder);
        if(lastOrder==null) {
            log.info("checkAndInitConnector, feishu latestOrder is empty");
            return Result.newSuccess();
        }
        Long nowTime = System.currentTimeMillis();
        if(entityById.getBindType()==BindTypeEnum.auto) {
            if(StringUtils.isNotEmpty(dataCenterId)) {
                ModuleFlag moduleFlag = fsOrderManager.judgeFeishuConnectorModule(tenantId);
                Long orderEndTime = lastOrder.getEndTime();
                log.info("checkAndInitConnector,nowTime={},orderEndTime={},moduleFlag={}",nowTime,
                        orderEndTime,moduleFlag);
                //检查客户的license是否过期，如果客户已经重新下飞书版CRM订单，则自动帮客户下0元的飞书连接器订单
                if(orderEndTime!=null
                        && moduleFlag!=null
                        && !moduleFlag.isFlag()
                        && orderEndTime > nowTime) {
                    com.facishare.open.order.contacts.proxy.api.result.Result<Void> buyConnector = orderService.buyConnector(fsEa,
                            "0",
                            lastOrder.getBeginTime(),
                            orderEndTime);
                    log.info("checkAndInitConnector,auto bind,repaid,buyConnector={}",buyConnector);
                    return buyConnector.isSuccess() ? Result.newSuccess() : Result.newError(buyConnector.getCode(),buyConnector.getMsg());
                }
            }
        }
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> buyConnector = orderService.buyConnector(fsEa,
                "0",
                lastOrder.getBeginTime(),
                lastOrder.getEndTime());
        log.info("checkAndInitConnector,buyConnector={}",buyConnector);

        if(!buyConnector.isSuccess()) {
            return Result.newError(ResultCodeEnum.FEISHU_CONNECTOR_INIT_FAILED);
        }

        return Result.newSuccess();
    }

    @Override
    public Result<OuterOaEnterpriseBindEntity> getEnterpriseBind(String outEa, String fsEa) {
        return Result.newSuccess(enterpriseBindManager.getEnterpriseBind(outEa, fsEa));
    }

    @Override
    public Result<Void> cloudFsBindWithFeishu(String outEa, String fsEa,String appId, String domain) {
        OuterOaEnterpriseBindEntity enterpriseBindEntity = enterpriseBindManager.getEnterpriseBind(outEa, fsEa);
        if(ObjectUtils.isEmpty(enterpriseBindEntity)) {
            //插入
            FeishuEnterpriseConnectParams feishuEnterpriseConnectParams = FeishuEnterpriseConnectParams.builder().build();

            int count = enterpriseBindManager.insert(ChannelEnum.feishu,
                    fsEa,
                    outEa,
                    domain,
                    BindTypeEnum.manual,
                    BindStatusEnum.normal,JSONObject.toJSONString(feishuEnterpriseConnectParams),appId);
            log.info("EnterpriseBindServiceImpl.cloudFsBindWithFeishu,add,fsEa={},count={}", fsEa, count);
        } else {
            //更新
            int count = enterpriseBindManager.updateBindStatus(fsEa,outEa,BindStatusEnum.normal);
            log.info("EnterpriseBindServiceImpl.cloudFsBindWithFeishu,update,fsEa={},count={}", fsEa, count);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<OuterOaEnterpriseBindEntity> getEntity(String fsEa) {
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().fsEa(fsEa).channel(ChannelEnum.feishu).build();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager.getEntities(outerOaEnterpriseBindParams);
        if(CollectionUtils.isNotEmpty(entities)){
            return Result.newSuccess(entities.get(0));
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> updateConnectParams(FsBindWithFeishuArg feiShuConnectParam) {
        //现在这边支持lark。域名现在暂时lark.com
        String larkDomain="larksuite.com";
        String outEa=feiShuConnectParam.getOutEa();
        String displayid=feiShuConnectParam.getDisplayId();
        if(StringUtils.isAnyBlank(outEa,displayid)||feiShuConnectParam.getFeishuEnterpriseConnectParams()==null){
            return Result.newError(ResultCodeEnum.FEISHU_TENANT_OUT_EA_ERROR);
        }
        //TODO
        String appId=null;
        String appSecret=null;
        String fsEa=feiShuConnectParam.getFsEa();
        OuterOaEnterpriseBindEntity entityByOutEAafsEaAppId = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.feishu, fsEa, appId);
        if(ObjectUtils.isEmpty(entityByOutEAafsEaAppId)){
            //插入数据。TODO FIX
            FeishuEnterpriseConnectParams feishuEnterpriseConnectParams = FeishuEnterpriseConnectParams.builder().build();
            //后续会去掉ConfigCenter.crm_domain
            enterpriseBindManager.insert(ChannelEnum.feishu,
                    fsEa,
                    outEa,
                    ConfigCenter.crm_domain,
                    BindTypeEnum.manual,
                    BindStatusEnum.normal,JSONObject.toJSONString(feishuEnterpriseConnectParams),appId);
            CorpInfoEntity entity = CorpInfoEntity.builder()
                    .displayId(displayid)
                    .tenantKey(outEa)
                    .tenantName(feiShuConnectParam.getOutEn())
                    .tenantTag(feiShuConnectParam.getTenantTag())
                    .build();
            corpInfoManager.updateCorpInfo(entity);
        }else{
            String connectParams = entityByOutEAafsEaAppId.getConnectInfo();
            FeishuEnterpriseConnectParams feishuEnterpriseConnectParams = JSONObject.parseObject(connectParams, FeishuEnterpriseConnectParams.class);
            //TODO fix
//            feishuEnterpriseConnectParams.setAppId(appId);
//            feishuEnterpriseConnectParams.setAppSecret(appSecret);
            feishuEnterpriseConnectParams.setBaseUrl(larkDomain);
            entityByOutEAafsEaAppId.setAppId(appId);
            entityByOutEAafsEaAppId.setOutEa(outEa);
            entityByOutEAafsEaAppId.setConnectInfo(JSONObject.toJSONString(feishuEnterpriseConnectParams));
            outerOaEnterpriseBindManager.updateById(entityByOutEAafsEaAppId);

        }
        //3.判断集成连接信息是否存在，存在则更新
        int tenantId = eieaConverter.enterpriseAccountToId(fsEa);
        UpdateConnectParamsArg updateConnectParamsArg = new UpdateConnectParamsArg();
        updateConnectParamsArg.setTenantId(tenantId+"");
        updateConnectParamsArg.setDataCenterId(feiShuConnectParam.getDataCenterId());
        updateConnectParamsArg.setDataCenterName(feiShuConnectParam.getDataCenterName());
        updateConnectParamsArg.setConnectParams(JSONObject.toJSONString(feiShuConnectParam));
        erpdssManager.updateConnectParams(updateConnectParamsArg);

        return Result.newSuccess();
    }
}