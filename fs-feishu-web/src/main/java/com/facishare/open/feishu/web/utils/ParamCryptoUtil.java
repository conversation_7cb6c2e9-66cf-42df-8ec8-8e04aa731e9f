package com.facishare.open.feishu.web.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 参数加密解密工具类
 */
// IgnoreI18nFile
public class ParamCryptoUtil {
    private static final Logger logger = LoggerFactory.getLogger(ParamCryptoUtil.class);

    // 16字节AES密钥
    private static final String SECRET_KEY = "Fxiaoke@2024#Key";
    private static final SecretKeySpec KEY_SPEC = new SecretKeySpec(
            SECRET_KEY.getBytes(StandardCharsets.UTF_8), "AES");

    /**
     * 加密字符串
     */
    public static String encrypt(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }
        
        try {
            // 1. 创建加密器
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, KEY_SPEC);
            
            // 2. 加密
            byte[] encrypted = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
            
            // 3. Base64编码（URL安全）
            return Base64.getUrlEncoder().withoutPadding().encodeToString(encrypted);
        } catch (Exception e) {
            logger.error("加密失败: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解密字符串
     */
    public static String decrypt(String encryptedContent) {
        if (encryptedContent == null || encryptedContent.isEmpty()) {
            return encryptedContent;
        }
        
        try {
            // 1. Base64解码
            byte[] decoded = Base64.getUrlDecoder().decode(encryptedContent);
            
            // 2. 创建解密器
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, KEY_SPEC);
            
            // 3. 解密
            byte[] decrypted = cipher.doFinal(decoded);
            
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("解密失败: " + e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 加密URL参数（特别处理浏览器环境）
     * 先加密，然后确保URL安全
     */
    public static String encryptForUrl(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }
        
        try {
            // 1. 先进行标准加密
            String encrypted = encrypt(content);
            if (encrypted == null) {
                return null;
            }
            
            // 2. 进行URL编码，确保在浏览器传输过程中不被篡改
            // 注意：在大多数情况下这一步不是必需的，因为我们使用了URL安全的Base64编码
            // 但为了更高的兼容性，我们仍然添加此步骤
            return URLEncoder.encode(encrypted, StandardCharsets.UTF_8.name());
        } catch (Exception e) {
            logger.error("URL参数加密失败: " + e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 解密可能经过浏览器的加密URL参数
     * 先解析URL编码，然后解密
     */
    public static String decryptFromUrl(String encryptedContent) {
        if (encryptedContent == null || encryptedContent.isEmpty()) {
            return encryptedContent;
        }
        
        try {
            // 1. 先进行URL解码
            String decoded = URLDecoder.decode(encryptedContent, StandardCharsets.UTF_8.name());
            
            // 2. 然后进行标准解密
            return decrypt(decoded);
        } catch (Exception e) {
            // 尝试直接解密，可能参数并未被URL编码
            try {
                return decrypt(encryptedContent);
            } catch (Exception innerE) {
                logger.error("URL参数解密失败: " + e.getMessage(), e);
                return null;
            }
        }
    }

    public static void main(String[] args) {
        // 打印密钥信息
        System.out.println("密钥: " + SECRET_KEY);
        System.out.println("密钥长度: " + SECRET_KEY.getBytes(StandardCharsets.UTF_8).length + " 字节");
        /**
         * - - - "-" "58.250.250.46,172.31.101.8, 172.31.101.203" "58.250.250.46" - 10.34.21.189 [07/May/2025:15:42:33 +0800] "GET /feishu/internal/getFsEaList?outEa
         * =QHy6FeEkgmB-iDENI4Ovn-0UqGbEduMBMYYz4FWvGxw&outUserId=w-mVKjlguI1KuRe0JaH4e8tuErRpUl04UGD_w5dbMG77
         */
        System.out.printf(decrypt("QHy6FeEkgmB-iDENI4Ovn-0UqGbEduMBMYYz4FWvGxw"));
        System.out.printf(decrypt("w-mVKjlguI1KuRe0JaH4e8tuErRpUl04UGD_w5dbMG77"));
        // 测试数据
        String[] testData = {
            "普通文本9797979",
            "中文测试",
            "http://example.com?key=value",
            "特殊字符!@#$%^",
            "https://crm.ceshi112.com/hcrm/template/initpage?channel=feishu&outEa=yjMVRnwhOq6Ac5sZ_OJtW-0UqGbEduMBMYYz4FWvGxw&appId=c5RH-IgItCqnP0PK7NuvW3SMPBCoCarxdiNuWcRWrSM&userId=mkQCDAwwuL9tt77iPf96o0UJpWpmQ7SXjaZQXMBSKFxAY9_WRYG8oysIUkwu5dU1&fsEa=null&timeStamp=1746550616647",
            null
        };

        // 测试加密解密
        for (String data : testData) {
            System.out.println("\n原文: " + data);
            try {
                // 标准加密解密测试
                String encrypted = encrypt(data);
                System.out.println("标准加密: " + encrypted);
                
                if (encrypted != null) {
                    String decrypted = decrypt(encrypted);
                    System.out.println("标准解密: " + decrypted);
                    
                    boolean success = (data == null && decrypted == null) || 
                                    (data != null && data.equals(decrypted));
                    System.out.println("标准结果: " + (success ? "成功" : "失败"));
                } else {
                    System.out.println("标准加密失败");
                }
                
                // URL参数加密解密测试
                String urlEncrypted = encryptForUrl(data);
                System.out.println("URL加密: " + urlEncrypted);
                
                if (urlEncrypted != null) {
                    String urlDecrypted = decryptFromUrl(urlEncrypted);
                    System.out.println("URL解密: " + urlDecrypted);
                    
                    boolean urlSuccess = (data == null && urlDecrypted == null) || 
                                      (data != null && data.equals(urlDecrypted));
                    System.out.println("URL结果: " + (urlSuccess ? "成功" : "失败"));
                    
                    // 模拟浏览器重复URL编码的情况
                    try {
                        String doubleEncoded = URLEncoder.encode(urlEncrypted, StandardCharsets.UTF_8.name());
                        System.out.println("模拟浏览器二次编码: " + doubleEncoded);
                        String stillDecrypted = decryptFromUrl(doubleEncoded);
                        System.out.println("处理二次编码解密: " + stillDecrypted);
                        boolean stillSuccess = (data == null && stillDecrypted == null) || 
                                           (data != null && data.equals(stillDecrypted));
                        System.out.println("处理二次编码结果: " + (stillSuccess ? "成功" : "失败"));
                    } catch (Exception e) {
                        System.out.println("二次编码测试异常: " + e.getMessage());
                    }
                } else {
                    System.out.println("URL加密失败");
                }
            } catch (Exception e) {
                System.out.println("测试异常: " + e.getMessage());
            }
        }
    }
}