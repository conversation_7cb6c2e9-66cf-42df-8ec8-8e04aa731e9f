package com.facishare.open.feishu.syncapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_employee_bind")
public class EmployeeBindEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 来源
     */
    private ChannelEnum channel;
    /**
     * 纷享EA
     */
    private String fsEa;
    /**
     * 纷享用户ID
     */
    private String fsUserId;
    /**
     * 纷享员工负责人用户ID
     */
    private String fsLeaderUserId;

    /**
     * 目标企业EA
     */
    private String outEa;
    /**
     * 目标用户ID
     */
    private String outUserId;
    /**
     * 目标用户负责人用户ID
     */
    private String outLeaderUserId;

    /**
     * 绑定类型
     */
    private BindTypeEnum bindType;
    /**
     * 绑定状态
     */
    private BindStatusEnum bindStatus;

    private Date createTime;
    private Date updateTime;
}
