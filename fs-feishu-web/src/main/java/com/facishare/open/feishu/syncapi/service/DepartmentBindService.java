package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.entity.DepartmentBindEntity;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.organization.api.model.department.DepartmentDto;

import java.util.List;

public interface DepartmentBindService {
    /**
     * 缓存部门信息
     * @param fsEa 纷享ea
     * @param departmentDto fs部门信息
     * @return
     */
    Result<List<DepartmentDto>> fsDepartmentInfoCache(String fsEa, DepartmentDto departmentDto);

    Result<OuterOaDepartmentBindEntity> queryDepData(ChannelEnum channel, String fsEa, String fsDepId, String outEa, String outDepId);

    Result<Integer> insertDepData(DepartmentBindEntity entity);
}
