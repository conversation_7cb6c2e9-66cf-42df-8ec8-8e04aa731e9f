package com.facishare.open.feishu.syncapi.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.syncapi.model.CrmOrderProductVo;
import com.facishare.open.feishu.syncapi.model.config.VersionModel;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
// IgnoreI18nFile
public class ConfigCenter {
    public static String RESET_URL = "http://172.31.101.246:15056/";
    public static String feishuCrmAppId = null;
    public static String feishuEncryptKey;
    public static String FEISHU_WEB_URL = "https://applink.feishu.cn/client/web_url/open?mode={mode}&url={url}";
    public static String FEISHU_AUTHEN_URL = "https://open.feishu.cn/open-apis/authen/v1/index?app_id={app_id}&state={state}&redirect_uri={redirect_uri}";
    public static String CRM_AUTH_URL = "https://crm.ceshi112.com/feishu/external/loginAuth";
    public static String CRM_FUNCTION_URL = "/hcrm/feishu/function/";
    public static String CRM_COMMON_WEBVIEW = "ava-fs-common-webview?redirect=1&url=";
    public static String CRM_TODO_URL = "todo?apiname={apiname}&id={id}&ea={ea}";
    public static String CRM_BPM_URL = "bpm?workflowInstanceId={workflowInstanceId}&taskId={taskId}&apiname={apiname}&id={id}";
    public static String CRM_FILE_URL = "/dps/preview/bypath?path={path}&showHeader=1";
    public static String CRM_REDIRECT_URL = "redirect?redirectUrl={redirectUrl}";
    public static String CRM_APPROVAL_BRIDGE_URL = "approval-bridge?workflowInstanceId={workflowInstanceId}&objectApiName={objectApiName}&objectId={objectId}&ea={ea}";
    public static String feishuConnectorOpenUrl;
    public static String feishuConnectorProductId;
    public static String feishuConnectorProductCode;
    //需要鉴权的接口列表
    public static List<String> needAuthInterfaceList;
    //合法的用户角色
    public static List<String> validRoleCodeList;
    public static Map<String,List<Integer>> superAdminMap;
    public static Boolean SYNC_SEND_MSG = Boolean.FALSE;
    public static String FEISHU_SEND_MSG_URL;
    public static Boolean FEISHU_IS_ADD_TODO = Boolean.FALSE;
    /**
     * 文件预览路径，%s为带ext的npath
     */
    public static String PREVIEW_FILE_PATH = "https://www.ceshi112.com/FSC/EM/File/GetByPath?path=%s";
    /**
     * 消息体最长数量
     */
    public static Integer NOTICE_MAX_SIZE = 500;
    /**
     * erp数据同步appId，这里共用一下
     */
    public static String ERP_SYNC_DATA_APP_ID = "FSAID_9897f5";

    /**
     * 接收告警人
     */
    public static Set<String> NOTIFICATION_MEMBERS = Sets.newHashSet();

    /**
     * 企业开通接收告警人
     */
    public static Set<String> ENTERPRISE_OPEN_NOTIFICATION_MEMBERS = Sets.newHashSet();

    /**
     * 接收告警企业
     */
    public static String NOTIFICATION_EA = "fszdbd2575";


    public static JSONObject feishuEdition = null;

    /**
     * Xor加密密钥
     */
    public static String XOR_SECRET_KEY= "";

    public static Set<String> AUTO_BIND_ACCOUNT_BY_NAME_EA = Sets.newHashSet();

    public static Set<String> AUTO_BIND_ACCOUNT_BY_EMPLOYEE_NUMBER = Sets.newHashSet();

    /**
     * 专属云是否推送消息到纷享云
     */
    public static Boolean IS_SEND_MSG_BY_CLOUD = Boolean.FALSE;

    public static String CRM_RATE_LIMIT = "{\"defaultRateLimit\":20,\"84883\":2}";

    public static String OUT_LIMIT_TIMES = "1000";

    public static String FILTER_MESSAGES_EA = "{\"85903\":\"CstmCtrl_5FrKx__c\"}";

    public static String PAAS_FUNCTION_URL = "http://172.31.100.247:4859";

    public static String USE_TOOLS_ACCOUNT = "{\"84883\":[1021,1000]}";;//"{\"84883\":[\"1021\",\"1000\"]}";
    /**
     * 企业灰度审批新页面
     */
    public static Set<String> TODO_GRAY_EA;

    public static Set<String> CALENDAR_GRAY_EA = Sets.newHashSet();

    public static String ERROR_PAGE_TOKEN = "";

    public static String ERPDSS_OACONNECTOR_WEB_URL = "http://172.31.100.247:16363/inner/oaConnector";

    public static Boolean isOpenLog = Boolean.TRUE;

    public static Integer createCrmAccount = 30;

    public static String crm_domain;

    /**
     * 判断该环境是否是主环境
     */
    public static Boolean MAIN_ENV = false;

    //密钥
    public static String BASE64_SECRET = "";

    public static String APPROVAL_ACTION_CALLBACK_URL;
    public static String APPROVAL_ACTION_CALLBACK_TOKEN = "BiyJ6wa7yhYe6nvXNcZBOe0qix46kgWO001";
    public static String APPROVAL_ACTION_CALLBACK_KEY = "fxiaoke";

    public static String feishu_scan_code_auth_success_redirect_url = "https://a9.fspage.com/FSR/weex/erpdss/oa-pages.html";
    public static String feishu_scan_code_auth_redirect_url = "";

    public static String GET_TOKEN_URL = "%s/api/v1/oauth2/token?grant_type=authorization_code&code=%s";

    public static String GET_AUTH_USER_INFO="%s/api/v1/oauth2/userinfo";

    public static String SSO_REDIRECT_URL="/FHH/EM0HXUL/SSO/Login?token=%s";
    public static String SSO_WEB_URL="/XV/Home/Index#crm/detail/=/";
    public static String SSO_H5_URL="https://www.ceshi112.com/hcrm/dingtalk";

    /**
     * 卖家中心密钥。2.0貌似不直接使用该秘钥做验证了
     */
    public static String ACCESS_KEY = "52f821c4-175a-4550-a63d-b1dbb20d9cdb"; // TODO:开发时临时使用，记得改为空串
    //52f821c4-175a-4550-a63d-b1dbb20d9cdb
    /**
     * 基础接口调试秘钥
     */
    public static String SIGN_ACCESS_KEY = "cbe0de9669b9ee626e243e5d3e23daf8de1320c8486f164e7b82f03ec9d1fe29";
    /**
     * Kit接口调试秘钥
     */
    public static String KIT_ACCESS_KEY = "2c44ce237c20bec5ebee712d124387fcf751da85c985d26ebf8ec665aea41d5d";
    /**
     * 商家创建应用时提供给华为云商店的公钥所对应的私钥，RSA加密（密钥长度>=3072bit,填充方式OAEP）
     */
    public static String PRIVATE_KEY = "";
    /**
     * 访问密钥Access Key ID
     */
    public static String HUAWEICLOUD_SDK_AK = "RD6OOMGKBY9BWJVWTIDG";
    /**
     * 访问密钥Secret Access Key
     */
    public static String HUAWEICLOUD_SDK_SK = "bVNpQ6d0OlW22pOFfSkHvhstaf9f8X5HJwHDAq3O";
    /**
     * 查询华为云商定订单的URL
     */
    public static String ORDER_QUERY_URL = "https://mkt.myhuaweicloud.com/api/mkp-openapi-public/global/v1/order/query";
    /**
     * verify ssl certificate (true) or do not verify (false)
     * 需要对mkt.myhuaweicloud.com HTTPS证书进行强校验，不能忽略证书校验，从而保证调用的是真实而非伪造的云商店服务
     */
    public static boolean DO_VERIFY = true;
    /**
     * 加密的长度。
     */
    public static int ENCRYPT_LENGTH;
    /**
     * 返回前台地址。
     */
    public static String FRONT_END_URL;
    /**
     * 新购订单，返回备注信息。
     */
    public static String REMARK;
    /**
     * 企业账户
     */
    public static String ENTERPRISE_ACCOUNT;
    /**
     * 应用ID
     */
    public static String APP_ID;
    /**
     * 消息接收用户列表
     */
//    public static List<Integer> TO_USER_LIST = Lists.newArrayList();

    public static String SIGN_TYPE;
    /**
     * 产品标识
     */
    public static String SKU_CODE = "[{\"code\":\"0a4d1578-5295-46a7-92d4-7c803dccc51d\",\"name\":\"旗舰版\"},{\"code\":\"2d5750a6-cd45-4db0-855b-69406301bba6\",\"name\":\"旗舰增强版\"}]";

    public static String PRIVATE_KEY_CLIENT = "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";

    public static String HUAWEI_APP_ID="a459e1ebca06447e96687236f9f20af4";

    public static Integer MASTER_EA = 76517;

    public static String CRM_CUSTOM_ENTERPRISE_ID_FILE = "UDSText2__c";

    /**
     * 飞书isv应用对应的域名
     */
    public static  Map<String,String> FEISHU_ISV_REQUEST_MAP;

    public static String crm_product_ids;

    public static String try_order_tenant_id;

    public static String CURRENT_CHANNEL_ISV_CHANNEL="feishu";//默认当前环境的渠道是飞书

    public static String LARK_CONSTANTS="lark";

    public static String LARK_POINT_PLANT_ID="price_a3cc35bc41aa9013";

    public static Integer PUSH_NOTIFY_LIMIT_EMPLOYEES=50;

    public static String CRM_WEB_INTERCONNECT_TODO_URL = "/XV/Cross/Portal?ptype=detail&apiname={apiname}&id={dataId}&fs_out_appid={fsOutAppId}&appId={appId}";
    public static String CRM_INTERCONNECT_TODO_URL = "/proj/prm/feishu?fsAppId={fsAppId}&upstreamEa={upstreamEa}&apiName={apiName}&dataId={dataId}&webhash={webhash}#/uipaas_custom/object_detail/pages/detail/index";
    public static String CRM_INTERCONNECT_APPROVAL_BRIDGE_URL = "/proj/prm/feishu?fsAppId={fsAppId}&upstreamEa={upstreamEa}&apiName=ApprovalInstanceObj&dataId={workflowInstanceId}&objectApiName={objectApiName}&objectId={objectId}&forceRedirectH5={forceRedirectH5}/#/uipaas_custom/object_flow/pages/approval_bridge/approval_bridge";
    public static String CRM_INTERCONNECT_LOGIN_URL = "/fs-er-biz/er/auth/thirdSysLogin?upstreamEa={upstreamEa}&linkAppId={linkAppId}&redirectURL={redirectURL}&timestamp={timestamp}&verifyCode={verifyCode}&isRedirect={isRedirect}";
    public static String INTERCONNECT_TEM_SECRET_KEY = "{\"83998\":\"EFE54B3725150D55D8D896622C34B18F\"}";
    public static String INTERCONNECT_ENTERPRISE_TEMPLATE_ID = "5e5e00fwwwwwwwccccc65bb1";
    public static String INTERCONNECT_EMPLOYEE_TEMPLATE_ID = "5e5e00fwwwwwwwccccc65bb4";
    //应用id对应资源，因为飞书，lark现在是底层共用
    public static Map<String,String> FEISHU_LARK_APP_ID_CHANNEL= Maps.newHashMap();
    //应用对应的其他产品
    public static Map<String,List<CrmOrderProductVo>> app_other_product_map = Maps.newHashMap();
    static {
        ConfigFactory.getInstance().getConfig("fs-feishu-config", config -> {
            RESET_URL = config.get("RESET_URL", RESET_URL);
            feishuCrmAppId = config.get("feishu_crm_app_id");
            feishuEncryptKey = config.get("feishu_encrypt_key");
            feishuConnectorOpenUrl = config.get("feishu_connector_open_url");
            feishuConnectorProductId = config.get("connector.feishu.productId");
            feishuConnectorProductCode = config.get("connector.feishu.productCode");

            //feishu_edition_info={"cli_a20192f6afb8d00c":{"price_a24125af21eb100d":[{"version":"FEISHU_STANDARD","productId":"62d9367ec669560001ec6caf"}],"price_a25f5f67a172d00e":[{"version":"FEISHU_PRO","productId":"62d936c9c669560001ec6eeb"}],"price_a245f2680099100b":[{"version":"FEISHU_ULTIMATE","productId":"62d936fac669560001ec735d"}]}}
            String feishuEditionInfo = config.get("feishu_edition_info");
            feishuEdition = JSON.parseObject(feishuEditionInfo);
            LogUtils.info("OrderPaidEventHandler.init,feishuEdition={}", feishuEdition);
            FEISHU_WEB_URL = config.get("FEISHU_WEB_URL", FEISHU_WEB_URL);
            FEISHU_AUTHEN_URL = config.get("FEISHU_AUTHEN_URL", FEISHU_AUTHEN_URL);
            CRM_AUTH_URL = config.get("CRM_AUTH_URL", CRM_AUTH_URL);
            CRM_FUNCTION_URL = config.get("CRM_FUNCTION_URL", CRM_FUNCTION_URL);
            CRM_COMMON_WEBVIEW = config.get("CRM_COMMON_WEBVIEW", CRM_COMMON_WEBVIEW);
            CRM_TODO_URL = config.get("CRM_TODO_URL", CRM_TODO_URL);
            CRM_BPM_URL = config.get("CRM_BPM_URL", CRM_BPM_URL);
            PREVIEW_FILE_PATH = config.get("PREVIEW_FILE_PATH", PREVIEW_FILE_PATH);
            NOTICE_MAX_SIZE = config.getInt("NOTICE_MAX_SIZE", NOTICE_MAX_SIZE);
            ERP_SYNC_DATA_APP_ID = config.get("ERP_SYNC_DATA_APP_ID", ERP_SYNC_DATA_APP_ID);
            NOTIFICATION_EA = config.get("NOTIFICATION_EA", NOTIFICATION_EA);
            NOTIFICATION_MEMBERS =  ImmutableSet.copyOf(Splitter.on(",").split(config.get("NOTIFICATION_MEMBERS", "")));
            ENTERPRISE_OPEN_NOTIFICATION_MEMBERS =  ImmutableSet.copyOf(Splitter.on(",").split(config.get("ENTERPRISE_OPEN_NOTIFICATION_MEMBERS", "")));
            XOR_SECRET_KEY = config.get("XOR_SECRET_KEY", XOR_SECRET_KEY);
            AUTO_BIND_ACCOUNT_BY_NAME_EA =  ImmutableSet.copyOf(Splitter.on(";").split(config.get("AUTO_BIND_ACCOUNT_BY_NAME_EA", "")));
            AUTO_BIND_ACCOUNT_BY_EMPLOYEE_NUMBER =  ImmutableSet.copyOf(Splitter.on(";").split(config.get("AUTO_BIND_ACCOUNT_BY_EMPLOYEE_NUMBER", "")));
            needAuthInterfaceList = JSONArray.parseArray(config.get("need_auth_interface_list","[]"),String.class);
            validRoleCodeList = JSONArray.parseArray(config.get("valid_role_code_list","[]"),String.class);
            superAdminMap = JSONObject.parseObject(config.get("super_admin_map","{}"),Map.class);
            SYNC_SEND_MSG = config.getBool("SYNC_SEND_MSG", Boolean.FALSE);
            FEISHU_SEND_MSG_URL = config.get("FEISHU_SEND_MSG_URL");
            FEISHU_IS_ADD_TODO = config.getBool("FEISHU_IS_ADD_TODO", Boolean.FALSE);
            IS_SEND_MSG_BY_CLOUD = config.getBool("IS_SEND_MSG_BY_CLOUD", IS_SEND_MSG_BY_CLOUD);
            CRM_RATE_LIMIT = config.get("CRM_RATE_LIMIT", CRM_RATE_LIMIT);
            OUT_LIMIT_TIMES = config.get("OUT_LIMIT_TIMES", OUT_LIMIT_TIMES);
            FILTER_MESSAGES_EA = config.get("FILTER_MESSAGES_EA", FILTER_MESSAGES_EA);
            PAAS_FUNCTION_URL = config.get("PAAS_FUNCTION_URL", PAAS_FUNCTION_URL);
            USE_TOOLS_ACCOUNT = config.get("USE_TOOLS_ACCOUNT", USE_TOOLS_ACCOUNT);
            CALENDAR_GRAY_EA =  ImmutableSet.copyOf(Splitter.on(",").split(config.get("CALENDAR_GRAY_EA", "")));
            ERROR_PAGE_TOKEN = config.get("ERROR_PAGE_TOKEN", ERROR_PAGE_TOKEN);
            ERPDSS_OACONNECTOR_WEB_URL = config.get("ERPDSS_OACONNECTOR_WEB_URL", ERPDSS_OACONNECTOR_WEB_URL);
            CRM_APPROVAL_BRIDGE_URL = config.get("CRM_APPROVAL_BRIDGE_URL", CRM_APPROVAL_BRIDGE_URL);
            TODO_GRAY_EA =  ImmutableSet.copyOf(Splitter.on(";").split(config.get("TODO_GRAY_EA", "")));
            isOpenLog = config.getBool("isOpenLog", isOpenLog);
            createCrmAccount = config.getInt("createCrmAccount", 30);
            crm_domain = config.get("crm_domain", crm_domain);
            MAIN_ENV = config.getBool("MAIN_ENV", MAIN_ENV);
            BASE64_SECRET = config.get("BASE64_SECRET", BASE64_SECRET);
            APPROVAL_ACTION_CALLBACK_URL = config.get("APPROVAL_ACTION_CALLBACK_URL", "/erpdss/feishu/event/push");
            APPROVAL_ACTION_CALLBACK_TOKEN = config.get("APPROVAL_ACTION_CALLBACK_TOKEN", APPROVAL_ACTION_CALLBACK_TOKEN);
            APPROVAL_ACTION_CALLBACK_KEY = config.get("APPROVAL_ACTION_CALLBACK_KEY", APPROVAL_ACTION_CALLBACK_KEY);
            feishu_scan_code_auth_success_redirect_url = config.get("feishu_scan_code_auth_success_redirect_url", feishu_scan_code_auth_success_redirect_url);
            feishu_scan_code_auth_redirect_url = config.get("feishu_scan_code_auth_redirect_url", feishu_scan_code_auth_redirect_url);
            ACCESS_KEY = StringUtils.defaultString(config.get("ACCESS_KEY"), ACCESS_KEY);
            SIGN_ACCESS_KEY = config.get("SIGN_ACCESS_KEY", SIGN_ACCESS_KEY);
            KIT_ACCESS_KEY = config.get("KIT_ACCESS_KEY", KIT_ACCESS_KEY);
            PRIVATE_KEY = config.get("PRIVATE_KEY", PRIVATE_KEY);
            HUAWEICLOUD_SDK_AK = config.get("HUAWEICLOUD_SDK_AK", HUAWEICLOUD_SDK_AK);
            HUAWEICLOUD_SDK_SK = config.get("HUAWEICLOUD_SDK_SK", HUAWEICLOUD_SDK_SK);
            ORDER_QUERY_URL = config.get("ORDER_QUERY_URL", ORDER_QUERY_URL);
            DO_VERIFY = config.getBool("DO_VERIFY", true);

            ENCRYPT_LENGTH = config.getInt("ENCRYPT_LENGTH");
            FRONT_END_URL = config.get("FRONT_END_URL");
            REMARK = config.get("REMARK");
            ENTERPRISE_ACCOUNT = config.get("ENTERPRISE_ACCOUNT");
            APP_ID = config.get("APP_ID");
//            TO_USER_LIST = Splitter.on(",").splitToList(config.get("TO_USER_LIST")).stream().map(Integer::valueOf).collect(Collectors.toList());
            SIGN_TYPE = config.get("SIGN_TYPE");
            SKU_CODE = config.get("SKU_CODE");
            SSO_REDIRECT_URL = config.get("SSO_REDIRECT_URL");
            SSO_WEB_URL = config.get("SSO_WEB_URL");
            SSO_H5_URL = config.get("SSO_H5_URL");
            HUAWEI_APP_ID = config.get("HUAWEI_APP_ID", HUAWEI_APP_ID);
            LogUtils.info("OrderPaidEventHandler.init,HUAWEI_APP_ID={}", HUAWEI_APP_ID);
            PRIVATE_KEY_CLIENT = config.get("PRIVATE_KEY_CLIENT", PRIVATE_KEY_CLIENT);
            MASTER_EA = config.getInt("MASTER_EA", MASTER_EA);
            CRM_CUSTOM_ENTERPRISE_ID_FILE = config.get("CRM_CUSTOM_ENTERPRISE_ID_FILE", CRM_CUSTOM_ENTERPRISE_ID_FILE);
            FEISHU_ISV_REQUEST_MAP= JSONObject.parseObject(config.get("FEISHU_ISV_REQUEST_MAP","{}"),Map.class);
            FEISHU_LARK_APP_ID_CHANNEL= JSONObject.parseObject(config.get("FEISHU_LARK_APP_ID_CHANNEL","{}"),Map.class);
            crm_product_ids = config.get("crm_product_ids", crm_product_ids);
            try_order_tenant_id = config.get("try_order_tenant_id", try_order_tenant_id);
            CRM_WEB_INTERCONNECT_TODO_URL = config.get("CRM_WEB_INTERCONNECT_TODO_URL", CRM_WEB_INTERCONNECT_TODO_URL);
            CRM_INTERCONNECT_TODO_URL = config.get("CRM_INTERCONNECT_TODO_URL", CRM_INTERCONNECT_TODO_URL);
            CRM_INTERCONNECT_APPROVAL_BRIDGE_URL = config.get("CRM_INTERCONNECT_APPROVAL_BRIDGE_URL", CRM_INTERCONNECT_APPROVAL_BRIDGE_URL);
            CRM_INTERCONNECT_LOGIN_URL = config.get("CRM_INTERCONNECT_LOGIN_URL", CRM_INTERCONNECT_LOGIN_URL);
            INTERCONNECT_TEM_SECRET_KEY = config.get("INTERCONNECT_TEM_SECRET_KEY", INTERCONNECT_TEM_SECRET_KEY);
            INTERCONNECT_ENTERPRISE_TEMPLATE_ID = config.get("INTERCONNECT_ENTERPRISE_TEMPLATE_ID", INTERCONNECT_ENTERPRISE_TEMPLATE_ID);
            INTERCONNECT_EMPLOYEE_TEMPLATE_ID = config.get("INTERCONNECT_EMPLOYEE_TEMPLATE_ID", INTERCONNECT_EMPLOYEE_TEMPLATE_ID);
            CURRENT_CHANNEL_ISV_CHANNEL = config.get("CURRENT_CHANNEL_ISV_CHANNEL", CURRENT_CHANNEL_ISV_CHANNEL);
            app_other_product_map = JSONObject.parseObject(
                    config.get("app_other_product_map","{}"),
                    new TypeReference<Map<String, List<CrmOrderProductVo>>>() {}
            );
            PUSH_NOTIFY_LIMIT_EMPLOYEES = config.getInt("PUSH_NOTIFY_EMPLOYEES", PUSH_NOTIFY_LIMIT_EMPLOYEES);

        });
    }

    public static JSONArray getVersionArray(String appId,String pricePlanId) {
        JSONObject editionObj = feishuEdition.getJSONObject(appId);
        JSONArray editionArray = null;
        if(ObjectUtils.isNotEmpty(editionObj)) {
            editionArray = editionObj.getJSONArray(pricePlanId);
        }
        LogUtils.info("ConfigCenter.getVersionArray,editionArray={}",editionArray);

        return editionArray;
    }

    public static VersionModel getFirstVersionProductId(String appId, String pricePlanId) {
        JSONArray editionArray = getVersionArray(appId, pricePlanId);
        //定向方案这里取不到
        VersionModel model = null;
        if(ObjectUtils.isNotEmpty(editionArray)) {
            model = JSONObject.parseObject(editionArray.getJSONObject(0).toJSONString(),VersionModel.class);
        }
        return model;
    }
}
