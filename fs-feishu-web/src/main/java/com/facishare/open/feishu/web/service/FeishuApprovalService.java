package com.facishare.open.feishu.web.service;

import com.facishare.open.feishu.web.result.FeishuTaskQueryResult;
import com.facishare.open.feishu.syncapi.result.Result;

public interface FeishuApprovalService {
    /**
     * 查询飞书待办任务
     * @param outEa 飞书企业ID
     * @param userId 飞书用户ID
     * @param topic 任务类型 1:待办
     * @return 任务查询结果
     */
    Result<FeishuTaskQueryResult> queryTasks(String outEa, String userId, Integer topic);
} 