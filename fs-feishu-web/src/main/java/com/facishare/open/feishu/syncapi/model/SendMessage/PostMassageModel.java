package com.facishare.open.feishu.syncapi.model.SendMessage;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PostMassageModel implements Serializable {
    private LanguageModel post;

    @Data
    public static class LanguageModel implements Serializable{
        private TextMessage zh_cn;

        @Data
        public static class TextMessage implements Serializable{
            private String title;
            private List<List<Content>> content;

            @Data
            public static class Content implements Serializable{
                private String tag;
                private String text;
                private String href;
            }
        }
    }
}
