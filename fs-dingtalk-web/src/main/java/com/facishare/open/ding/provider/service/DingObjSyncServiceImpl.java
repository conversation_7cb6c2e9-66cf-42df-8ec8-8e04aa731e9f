package com.facishare.open.ding.provider.service;

import com.facishare.open.ding.api.service.DingObjSyncService;
import com.facishare.open.ding.api.vo.DingObjSyncVo;
import com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.isv.DingObjSyncPgEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.dingtalkisv.DingObjSyncPgMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/2/17 14:26
 * @Version 1.0
 */
@Service("dingObjSyncServiceImpl")
public class DingObjSyncServiceImpl implements DingObjSyncService {

    @Autowired
    private DingObjSyncPgMapper dingObjSyncPgMapper;

    @Override
    public Integer insertObjSync(DingObjSyncVo dingObjSyncVo) {
        DingObjSyncPgEntity entity = new DingObjSyncPgEntity();
        BeanUtils.copyProperties(dingObjSyncVo, entity);
        return dingObjSyncPgMapper.insertObjSync(entity);
    }

    @Override
    public Integer getObjById(String dingObjId, String corpId, String dingApiName) {
        return dingObjSyncPgMapper.getObjById(dingObjId, corpId, dingApiName);
    }
}
