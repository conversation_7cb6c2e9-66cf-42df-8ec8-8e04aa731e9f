package com.facishare.open.ding.transfer.handler;

import com.facishare.open.ding.provider.dao.ObjectDataCacheDao;
import com.facishare.open.ding.provider.entity.ObjectDataCacheEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.isv.ObjectDataCachePgEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.dingtalkisv.ObjectDataCachePgMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 对象数据缓存迁移处理器
 * <AUTHOR>
 * @date 2024/1/22 15:46:25
 */
@Component
public class DingTalkIsvObjectDataCacheHandler extends DingTalkIsvHandler<ObjectDataCacheEntity, ObjectDataCachePgEntity> {

    @Autowired
    private ObjectDataCacheDao objectDataCacheDao;

    @Autowired
    private ObjectDataCachePgMapper objectDataCachePgMapper;

    @Override
    protected void update(int enterpriseId, ObjectDataCacheEntity sourceData, ObjectDataCachePgEntity targetData) {
        final ObjectDataCachePgEntity entity = convert2PgEntity(sourceData, targetData);
        if (Objects.isNull(entity)) {
            return;
        }
        if (Objects.isNull(targetData)) {
            objectDataCachePgMapper.insert(entity);
        } else {
            objectDataCachePgMapper.updateById(entity);
        }
    }

    private ObjectDataCachePgEntity convert2PgEntity(ObjectDataCacheEntity sourceData, ObjectDataCachePgEntity targetData) {
        final ObjectDataCachePgEntity entity = new ObjectDataCachePgEntity();
        // 保持ID一致
        entity.setId(sourceData.getId());
        entity.setDataCode(sourceData.getDataCode());
        entity.setObjectDataId(sourceData.getObjectDataId());
        entity.setCorpId(sourceData.getCorpId());
        entity.setEi(sourceData.getEi());
        entity.setAppId(sourceData.getAppId());
        entity.setObjectApiName(sourceData.getObjectApiName());
        entity.setJsonData(sourceData.getJsonData());
        entity.setSynced(sourceData.isSynced());
        entity.setSyncFailed(sourceData.isSyncFailed());
        entity.setFailedReason(sourceData.getFailedReason());
        entity.setSyncDirection(sourceData.getSyncDirection());
        entity.setDeleted(sourceData.isDeleted());
        entity.setCreateTime(sourceData.getCreateTime());
        entity.setUpdateTime(sourceData.getUpdateTime());
        return entity;
    }

    @Override
    protected boolean checkDataEquals(ObjectDataCacheEntity sourceData, ObjectDataCachePgEntity targetData) {
        if (Objects.isNull(targetData)) {
            return false;
        }

        return Objects.equals(targetData.getDataCode(), sourceData.getDataCode()) &&
                Objects.equals(targetData.getObjectDataId(), sourceData.getObjectDataId()) &&
                Objects.equals(targetData.getCorpId(), sourceData.getCorpId()) &&
                Objects.equals(targetData.getEi(), sourceData.getEi()) &&
                Objects.equals(targetData.getAppId(), sourceData.getAppId()) &&
                Objects.equals(targetData.getObjectApiName(), sourceData.getObjectApiName()) &&
                Objects.equals(targetData.getJsonData(), sourceData.getJsonData()) &&
                Objects.equals(targetData.getSynced(), sourceData.isSynced()) &&
                Objects.equals(targetData.getSyncFailed(), sourceData.isSyncFailed()) &&
                Objects.equals(targetData.getFailedReason(), sourceData.getFailedReason()) &&
                Objects.equals(targetData.getSyncDirection(), sourceData.getSyncDirection()) &&
                Objects.equals(targetData.getDeleted(), sourceData.isDeleted());
    }

    @Override
    protected ObjectDataCachePgEntity getTargetData(int enterpriseId, ObjectDataCacheEntity k) {
        return objectDataCachePgMapper.selectById(k.getId());
    }

    @Override
    protected List<ObjectDataCacheEntity> getSourceDataPage(Integer enterpriseId, ObjectDataCacheEntity maxId) {
        String id = Objects.isNull(maxId) ? null : maxId.getId();
        return objectDataCacheDao.pageById(enterpriseId, id, 1000);
    }
}