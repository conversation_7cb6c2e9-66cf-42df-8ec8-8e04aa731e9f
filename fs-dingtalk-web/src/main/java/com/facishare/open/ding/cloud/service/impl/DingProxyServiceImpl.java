package com.facishare.open.ding.cloud.service.impl;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.request.OapiServiceGetCorpTokenRequest;
import com.dingtalk.api.request.OapiSnsGetuserinfoBycodeRequest;
import com.dingtalk.api.response.OapiServiceGetCorpTokenResponse;
import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.facishare.open.ding.api.service.SyncBizDataService;
import com.facishare.open.ding.api.service.cloud.DingProxyService;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.Socket;

/**
 * <AUTHOR>
 * @Date 2021/5/23 15:29
 * @Version 1.0
 */
@Service("dingProxyServiceImpl")
@Slf4j
public class DingProxyServiceImpl implements DingProxyService {

    @Autowired
    private SyncBizDataService syncBizDataService;
    @Autowired
    private DingManager dingManager;


    @Override
    public String getUserId(String corpId,String unionId,String suiteId) {
        String userIdByUnionId = dingManager.getUserIdByUnionId(corpId, unionId,suiteId);
        return userIdByUnionId;
    }

    @Override
    public EmployeeDingVo getUserInfo(String corpId,String userId,String suiteId) {
        EmployeeDingVo empVo = dingManager.getUserByUserId(corpId, userId,suiteId);
        return empVo;
    }

    @Override
    public Result<Integer> updateSql(String sql) {

        return null;
    }


}
