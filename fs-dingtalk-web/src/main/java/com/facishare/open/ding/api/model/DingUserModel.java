package com.facishare.open.ding.api.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/8 10:05
 * @Version 1.0
 */
@Data
public class DingUserModel implements Serializable {
    private Boolean hasMore;
    private Long nextCursor;
    private List<UserResponse> list;
    @Data
    public static class UserResponse implements Serializable{
        private String userId;
        private String unionid;
        private String name;
        private String avatar;
        private String stateCode;
        private String mobile;
        private String title;
        private String jobNumber;
        private List<Long> deptIdList;
        private Long deptOrder;
        private Boolean admin;
        private Boolean boss;
        private Boolean leader;

    }

}
