package com.facishare.open.ding.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class ObjectDataCacheVo implements Serializable {
    private String id;
    private String dataCode;//对象数据编码
    private String objectDataId;//CRM对象数据ID
    private String corpId; //钉钉企业ID
    private int ei;//纷享ei
    private long appId;//钉钉应用ID
    private String objectApiName;//对象apiName
    private String jsonData;//对象json数据
    private boolean synced;//是否已经同步到钉钉连接器
    private boolean syncFailed;//是否同步失败
    private String failedReason;//同步失败的原因
    private int syncDirection;//数据同步方向 0 crm->钉钉连接器 1 钉钉连接器->crm
    private boolean deleted;//数据是否被作废或删除
    private Timestamp createTime;
    private Timestamp updateTime;
}
