package com.facishare.open.ding.api.service;

import com.facishare.open.ding.api.enums.SyncDirectionEnum;
import com.facishare.open.ding.api.model.SyncDataMappingModel;
import com.facishare.open.ding.common.result.Result;

import java.util.List;

public interface SyncDataMappingsService {

    Result<List<SyncDataMappingModel>> loadSyncDataMapping(Integer ei, String corpId, String crmObjectApiName, String dingObjApiName,
                                                           SyncDirectionEnum direction, String dataId);

    Result<Boolean> updateSyncDataMapping(SyncDataMappingModel mappingModel);

}
