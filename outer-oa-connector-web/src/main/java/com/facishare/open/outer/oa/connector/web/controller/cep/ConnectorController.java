package com.facishare.open.outer.oa.connector.web.controller.cep;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaAppInfoParams;
import com.facishare.open.outer.oa.connector.common.api.admin.*;
import com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import com.facishare.open.outer.oa.connector.common.api.params.SupportAppParams;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.facishare.open.outer.oa.connector.web.manager.OuterOASettingFactory;
import com.facishare.open.outer.oa.connector.common.api.params.CepArg;
import com.facishare.open.outer.oa.connector.common.api.result.OuterOAConnectSettingResult;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.common.api.utils.ConnectorVoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 连接器管理Controller
 */
@Slf4j
@RestController
@RequestMapping("/oaBase/connector")
public class ConnectorController extends BaseController {

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Autowired
    private OuterOASettingFactory outerOASettingFactory;


    /**
     * 返回连接器信息
     * 
     * @param arg
     * @return
     */
    @RequestMapping(value = "/getOAConnectInfo", method = RequestMethod.POST)
    public Result<OuterOAConnectSettingResult> getOAConnectInfo(@RequestBody CepArg arg,@RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {

        log.info("Start getting OA connect info for dcId: {}:lang:{}", arg.getCurrentDcId(),lang);
        try {
            // 参数校验
            if (arg == null || arg.getCurrentDcId() == null) {
                log.error("Invalid input parameters");
                return Result.newError(ResultCodeEnum.PARAMETER_ERROR);
            }

            String tenantId = getLoginUserTenantId();
            OuterOaEnterpriseBindEntity bindEntity = outerOaEnterpriseBindManager.getEntityById(arg.getCurrentDcId());
            if (ObjectUtils.isEmpty(bindEntity)) {
                log.warn("Enterprise bind not found for dcId: {}", arg.getCurrentDcId());
                return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
            }

            // OuterOaAppInfoParams appInfoParams = buildAppInfoParams(bindEntity);
            // OuterOaAppInfoEntity appInfoEntity =
            // outerOaAppInfoManager.getEntity(appInfoParams);
            // if (appInfoEntity == null) {
            // log.error("App info not found for channel: {}, outEa: {}",
            // bindEntity.getChannel(),
            // bindEntity.getOutEa());
            // return Result.newError(ResultCodeEnum.APP_ADMIN_NOT_BIND);
            // }
            OuterOAConnectSettingResult result = buildConnectSettingResult(bindEntity);
            log.info("Successfully got OA connect info for dcId: {}", arg.getCurrentDcId());
            return Result.newSuccess(result);
        } catch (Exception e) {
            log.error("Failed to get OA connect info for dcId: " + arg.getCurrentDcId(), e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    private OuterOaAppInfoParams buildAppInfoParams(OuterOaEnterpriseBindEntity bindEntity) {
        return OuterOaAppInfoParams.builder().channel(bindEntity.getChannel()).outEa(bindEntity.getOutEa())
                .appId(bindEntity.getAppId()).build();
    }

    private OuterOAConnectSettingResult buildConnectSettingResult(OuterOaEnterpriseBindEntity bindEntity) {
        OuterOAConnectSettingResult result = new OuterOAConnectSettingResult();
        OuterOAConnectSettingResult.ConnectParams connectParams = new OuterOAConnectSettingResult.ConnectParams();
        BaseConnectorVo connectorVo = new BaseConnectorVo();
        try {
            // 1. 获取对应的ConnectorVo类
            Class<? extends BaseConnectorVo> connectorClass = bindEntity.getChannel().getClassName();
            if (connectorClass == null) {
                log.warn("No connector class found for channel: {}, appType: {}", bindEntity.getChannel());
                return result;
            }
            // 2. 将String类型的connectInfo反序列化为对应的ConnectorVo
            connectorVo = JSONObject.parseObject(bindEntity.getConnectInfo(), connectorClass);
            if (connectorVo == null) {
                log.warn("Failed to parse connectInfo for channel: {}, appType: {}", bindEntity.getChannel());
                return result;
            }
            // 3. 将ConnectorVo设置到connectParams中
            ConnectorVoUtils.setConnectorVoToConnectParams(connectParams, connectorVo, bindEntity.getChannel());
        } catch (Exception e) {
            log.error("Error processing connector info: channel={}, appType={}, error={}", bindEntity.getChannel(),
                    e.getMessage(), e);
        }
        // 4. 设置其他必要信息
        result.setConnectParams(connectParams);
        result.setChannelEnum(bindEntity.getChannel());
        result.setBindTypeEnum(bindEntity.getBindType());
        result.setBindStatus(bindEntity.getBindStatus().name());
        result.setOuterOaAppInfoTypeEnum(connectorVo.getAppType());
        return result;
    }

    /**
     * 保存设置
     * 
     * @param settingResult
     * @return
     */
    @RequestMapping(value = "/saveConnectInfo", method = RequestMethod.POST)
    public Result<Void> saveConnectInfo(@RequestBody OuterOAConnectSettingResult settingResult) {
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = outerOaEnterpriseBindManager
                .getEntityById(settingResult.getCurrentDcId());
        if (ObjectUtils.isEmpty(outerOaEnterpriseBindEntity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        // OuterOASettingInterface 执行对应的逻辑。
        OuterAbstractSettingService implementation = outerOASettingFactory
                .getImplementation(settingResult.getChannelEnum(), settingResult.getOuterOaAppInfoTypeEnum());
        // 执行不同连接器的判断处理
        Result<Void> validateConnectorConfig = implementation.doValidateConfigAndSave(settingResult,
                settingResult.getChannelEnum(), settingResult.getOuterOaAppInfoTypeEnum());
        return validateConnectorConfig;

    }

    /**
     * 获取跳转链接
     *
     * @param cepArg
     * @return
     */
    @RequestMapping(value = "/getRedirectUrl", method = RequestMethod.POST)
    public Result<String> getRedirectUrl(@RequestBody CepArg cepArg) {

        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = outerOaEnterpriseBindManager
                .getEntityById(cepArg.getCurrentDcId());
        if (ObjectUtils.isEmpty(outerOaEnterpriseBindEntity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        // OuterOASettingInterface 执行对应的逻辑。
//        BaseConnectorVo baseConnectorVo = JSONObject.parseObject(outerOaEnterpriseBindEntity.getConnectInfo(), BaseConnectorVo.class);
//        baseConnectorVo.setDataCenterId(cepArg.getCurrentDcId());
        // OuterOASettingInterface 执行对应的逻辑。
        OuterAbstractSettingService implementation = outerOASettingFactory
                .getImplementation(outerOaEnterpriseBindEntity.getChannel(), OuterOaAppInfoTypeEnum.isv);
        // 执行不同连接器的判断处理
         Result<String> redirectUrl =implementation.doGetAuthUrl(cepArg.getCurrentDcId(),outerOaEnterpriseBindEntity.getChannel(), OuterOaAppInfoTypeEnum.isv);
        return redirectUrl;

    }


    /**
     * 获取可以绑定的应用
     * 暂时用不上
     *
     * @param supportAppParams
     * @return
     */
    @RequestMapping(value = "/getSupportConnectAppIds", method = RequestMethod.POST)
    public Result<List<SupportAppParams.AppInfoResult>> getSupportConnectAppIds(@RequestBody SupportAppParams supportAppParams) {
            //授权连接后，这里应用需要用外部企业查询对应appinfo表，找到没有被绑定企业
        List<OuterOaAppInfoEntity> outerOaAppInfoEntities = outerOaAppInfoManager.queryAppAuth(supportAppParams.getChannelEnum(), supportAppParams.getOutEa());
        List<SupportAppParams.AppInfoResult> appInfoResults = new ArrayList<>();

        for (OuterOaAppInfoEntity outerOaAppInfoEntity : outerOaAppInfoEntities) {
            SupportAppParams.AppInfoResult appInfoResult= new SupportAppParams.AppInfoResult();
            appInfoResult.setAppId(outerOaAppInfoEntity.getAppId());
            Map<String,String> appInfoMap = JSONObject.parseObject(outerOaAppInfoEntity.getAppInfo(),new TypeReference<Map<String,String>>(){});
            appInfoResult.setAppName(appInfoMap.get("name"));
            appInfoResults.add(appInfoResult);
        }
        return Result.newSuccess(appInfoResults);
    }


    /**
     * 取消连接
     *
     *
     */
    @RequestMapping(value = "/unbindConnect", method = RequestMethod.POST)
    public Result<String> unbindConnect(@RequestBody CepArg cepArg) {
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = outerOaEnterpriseBindManager
                .getEntityById(cepArg.getCurrentDcId());
        if (ObjectUtils.isEmpty(outerOaEnterpriseBindEntity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        // OuterOASettingInterface 执行对应的逻辑。
        OuterAbstractSettingService implementation = outerOASettingFactory
                .getImplementation(outerOaEnterpriseBindEntity.getChannel(), OuterOaAppInfoTypeEnum.isv);
        // 执行不同连接器的判断处理
        Result<String> unbindConnect =implementation.unbindConnect(outerOaEnterpriseBindEntity.getId(),outerOaEnterpriseBindEntity.getChannel());

        return unbindConnect;
    }

    /**
     * 获取对应支持消息类型
     *
     *
     */
    @RequestMapping(value = "/supportMessageTypes", method = RequestMethod.POST)
    public Result<List<AlertTypeEnum>> supportMessageTypes(@RequestBody CepArg cepArg) {
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = outerOaEnterpriseBindManager
                .getEntityById(cepArg.getCurrentDcId());
        if (ObjectUtils.isEmpty(outerOaEnterpriseBindEntity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        // OuterOASettingInterface 执行对应的逻辑。
        OuterAbstractSettingService implementation = outerOASettingFactory
                .getImplementation(outerOaEnterpriseBindEntity.getChannel(), OuterOaAppInfoTypeEnum.isv);
        // 执行不同连接器的判断处理
        List<AlertTypeEnum> channelSupportedAlertTypes = implementation.getChannelSupportedAlertTypes();

        return Result.newSuccess(channelSupportedAlertTypes);
    }

}