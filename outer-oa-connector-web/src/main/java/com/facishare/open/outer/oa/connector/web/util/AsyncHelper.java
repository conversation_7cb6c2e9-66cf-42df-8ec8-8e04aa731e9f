package com.facishare.open.outer.oa.connector.web.util;

import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.concurrent.*;
import java.util.function.Supplier;

/**
 * Spring MVC异步操作帮助类，实现超时控制 提供10秒超时处理机制： 1. 在10秒内完成，同步返回结果 2.
 * 超过10秒，返回超时提示，后台继续执行
 */
public class AsyncHelper {
    private static final Logger log = LoggerFactory.getLogger(AsyncHelper.class);
    private static final ExecutorService executor = Executors.newFixedThreadPool(10);

    // 默认超时时间（秒）
    private static final long DEFAULT_TIMEOUT_SECONDS = 10;

    /**
     * 创建DeferredResult，实现10秒超时控制
     * 
     * @param operation      要执行的异步操作
     * @param resultCodeEnum 错误时的结果代码
     * @param <T>            结果类型
     * @return 控制器可返回的DeferredResult对象
     */
    public static <T> DeferredResult<Result<T>> executeAsync(Supplier<Result<T>> operation,
            ResultCodeEnum resultCodeEnum) {
        return executeAsync(operation, DEFAULT_TIMEOUT_SECONDS, resultCodeEnum);
    }

    /**
     * 创建DeferredResult，实现自定义超时控制
     * 
     * @param operation      要执行的异步操作
     * @param timeoutSeconds 超时时间（秒）
     * @param resultCodeEnum 错误时的结果代码
     * @param <T>            结果类型
     * @return 控制器可返回的DeferredResult对象
     */
    public static <T> DeferredResult<Result<T>> executeAsync(Supplier<Result<T>> operation, long timeoutSeconds,
            ResultCodeEnum resultCodeEnum) {
        // 创建DeferredResult
        DeferredResult<Result<T>> deferredResult = new DeferredResult<>();

        try {
            // 提交任务到线程池并等待结果
            Future<Result<T>> future = executor.submit(operation::get);

            try {
                // 尝试在指定时间内同步获取结果
                Result<T> result = future.get(timeoutSeconds, TimeUnit.SECONDS);
                // 如果成功获取结果，直接返回
                deferredResult.setResult(result);
            } catch (TimeoutException e) {
                // 如果超时，返回提示信息
                deferredResult.setResult(Result.newError(resultCodeEnum));
                // 后台继续执行，不阻塞用户
                executor.execute(() -> {
                    try {
                        Result<T> finalResult = future.get();
                        log.info("execute success， {}", finalResult);
                    } catch (Exception ex) {
                        log.error("execute fail", ex);
                    }
                });
            } catch (InterruptedException e) {
                log.error("execute fail InterruptedException", e);
                deferredResult.setResult(Result.newError(resultCodeEnum));
                future.cancel(true);
                Thread.currentThread().interrupt();
            } catch (ExecutionException e) {
                log.error("execute fail ExecutionException", e);
                deferredResult.setResult(Result.newError(resultCodeEnum));
                future.cancel(true);
            }
        } catch (Exception e) {
            // 处理提交任务时的异常
            log.error("execute fail Exception", e);
            deferredResult.setResult(Result.newError(resultCodeEnum));
        }

        return deferredResult;
    }

    /**
     * 关闭线程池服务 应在应用程序关闭时调用
     */
    public static void shutdown() {
        executor.shutdown();
        try {
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}