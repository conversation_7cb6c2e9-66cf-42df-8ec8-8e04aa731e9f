<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd
        ">
    <context:component-scan base-package="com.facishare.open.huawei.kit.*"/>

    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="fs-feishu-config"/>
        <constructor-arg name="sectionNames" value="HUAIWEI_KIT_ENTERPRISE_REGISTER_SECTION"/>
        <constructor-arg name="messageListener" ref="enterpriseEventListener"/>
    </bean>

</beans>