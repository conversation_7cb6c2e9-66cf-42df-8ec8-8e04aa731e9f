package com.facishare.open.erpdss.outer.oa.connector.base.outer.event.ticket;

import com.facishare.open.erpdss.outer.oa.connector.base.HandlerTemplate;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;

/**
 * ticket事件处理器模板
 * <AUTHOR>
 * @date 2024-08-19
 */
public abstract class TicketEventHandlerTemplate extends HandlerTemplate {
    @Override
    public TemplateResult execute(Object data) {
        MethodContext context = MethodContext.newInstance(data);
        onTicketEvent(context);

        return context.getResult();
    }

    public abstract void onTicketEvent(MethodContext context);
}
