package com.facishare.open.ding.provider.task.crm;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;

/**
 * @<NAME_EMAIL>
 * @ClassName: DyncSyncManager
 * @Description: 动态调用对象同步接口
 * @datetime 2019/2/28 16:50
 * @Version 1.0
 */
@Slf4j
@Component
public class DyncSyncManager extends ApplicationObjectSupport {

    // 获取spring的上下文环境
   private ApplicationContext applicationContext;

    @PostConstruct
    public void init(){
        this.applicationContext = getApplicationContext();
    }

}
