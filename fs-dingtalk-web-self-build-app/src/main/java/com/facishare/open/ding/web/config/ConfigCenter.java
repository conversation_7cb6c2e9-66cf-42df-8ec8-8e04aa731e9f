package com.facishare.open.ding.web.config;

import com.alibaba.fastjson.JSONArray;
import com.facishare.rest.core.util.JacksonUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * Create by max on 2019/07/05
 **/
@Slf4j
public class ConfigCenter {

    public static Set<String> NEW_H5_GRAY_TENANTS = Sets.newHashSet();
    public static String APPROVAL_INSTANCE_OBJ = "ApprovalInstanceObj";
    public static List<String> QPS_LIMIT_CODE = Lists.newArrayList("90002", "Forbidden.AccessDenied.QpsLimitForApi", "90018", "Forbidden.AccessDenied.QpsLimitForAppkeyAndApi");
    public static String QPS_LIMIT_MAX_EA = "{\"default\":100,\"1111\":2}";
    public static Set<String> QPS_LIMIT_EA = Sets.newHashSet();
    public static Boolean SYNC_SEND_MSG = Boolean.FALSE;
    //需要鉴权的接口列表
    public static List<String> needAuthInterfaceList;
    //合法的用户角色
    public static List<String> validRoleCodeList;

    public static String CRM_RATE_LIMIT = "{\"defaultRateLimit\":20,\"84883\":2}";

    public static Set<String> BPM_TODO_EA = Sets.newHashSet();

    public static Set<String> TODO_GRAY_EA = Sets.newHashSet();

    public static String CRM_APPROVAL_INSTANCE_URL;

    /**
     * 新基座名单
     */
    public static Set<String> pagesEiSet = new HashSet<>();
    public static TreeMap<Integer, Integer> rangeMap = new TreeMap<>();
    public static Map<String,String> functionMaps= Maps.newHashMap();

    /**
     * 文件预览路径，%s为带ext的npath
     */
    public static String PREVIEW_FILE_PATH = "https://www.ceshi112.com/FSC/EM/File/GetByPath?path=%s";
    /**
     * 消息体最长数量
     */
    public static Integer NOTICE_MAX_SIZE = 500;
    /**
     * erp数据同步appId，这里共用一下
     */
    public static String ERP_SYNC_DATA_APP_ID = "FSAID_9897f5";
    /**
     * 接收告警人
     */
    public static Set<String> NOTIFICATION_MEMBERS = Sets.newHashSet();
    /**
     * 接收企信告警人
     */
    public static Set<String> ENTERPRISE_OPEN_NOTIFICATION_MEMBERS = Sets.newHashSet();
    /**
     * 接收告警企业
     */
    public static String NOTIFICATION_EA = "83384";

    static {
        ConfigFactory.getInstance().getConfig("fs-dingtalk-web", config -> {
            NEW_H5_GRAY_TENANTS =  ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("NEW_H5_GRAY_TENANTS", "")));
            QPS_LIMIT_MAX_EA = config.get("QPS_LIMIT_MAX_EA", QPS_LIMIT_MAX_EA);
            QPS_LIMIT_EA = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("QPS_LIMIT_EA", "")));
            SYNC_SEND_MSG = config.getBool("SYNC_SEND_MSG", Boolean.FALSE);

            needAuthInterfaceList = JSONArray.parseArray(config.get("need_auth_interface_list","[]"),String.class);
            validRoleCodeList = JSONArray.parseArray(config.get("valid_role_code_list","[]"),String.class);
            CRM_RATE_LIMIT = config.get("CRM_RATE_LIMIT", CRM_RATE_LIMIT);
            BPM_TODO_EA =  ImmutableSet.copyOf(Splitter.on(";").split(config.get("BPM_TODO_EA", "")));
            TODO_GRAY_EA =  ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("TODO_GRAY_EA", "")));
            CRM_APPROVAL_INSTANCE_URL = config.get("CRM_APPROVAL_INSTANCE_URL", CRM_APPROVAL_INSTANCE_URL);
            addOrUpdatePagesEis(config.get("NEW_PAGES_EI", ""));
            String mappingField = config.get("TODO_SEND_MESSAGE_FUNCTIONS", "{}");
            functionMaps = JacksonUtil.fromJson(mappingField, Map.class);
            PREVIEW_FILE_PATH = config.get("PREVIEW_FILE_PATH", PREVIEW_FILE_PATH);
            NOTICE_MAX_SIZE = config.getInt("NOTICE_MAX_SIZE", NOTICE_MAX_SIZE);
            NOTIFICATION_EA = config.get("NOTIFICATION_EA", NOTIFICATION_EA);
            NOTIFICATION_MEMBERS =  ImmutableSet.copyOf(Splitter.on(",").split(config.get("NOTIFICATION_MEMBERS", "1000")));
            ENTERPRISE_OPEN_NOTIFICATION_MEMBERS =  ImmutableSet.copyOf(Splitter.on(",").split(config.get("ENTERPRISE_OPEN_NOTIFICATION_MEMBERS", "")));

        });

    }

    public static void addOrUpdatePagesEis(String config) {
        //","分隔
        Set<String> eiConfigSet = ImmutableSet.copyOf(Splitter.on(",").split(config));

        for(String stringFile : eiConfigSet) {
            if(stringFile.contains(";")) {
                //";"分隔
                Set<String> pagesEiSet2 = ImmutableSet.copyOf(Splitter.on(";").split(stringFile));
                for(String stringFile2 : pagesEiSet2) {
                    if(stringFile2.contains("-")) {
                        String[] bounds = stringFile2.split("-");
                        int startRange = Integer.parseInt(bounds[0]);
                        int endRange = Integer.parseInt(bounds[1]);
                        rangeMap.put(startRange, endRange);
                    } else {
                        //普通
                        pagesEiSet.add(stringFile2);
                    }
                }
            } else {
                pagesEiSet.add(stringFile);
            }
        }
        log.info("ConfigCenter.addOrUpdatePagesEis.pagesEiSet={},rangeMap={}", pagesEiSet, rangeMap);
    }
}