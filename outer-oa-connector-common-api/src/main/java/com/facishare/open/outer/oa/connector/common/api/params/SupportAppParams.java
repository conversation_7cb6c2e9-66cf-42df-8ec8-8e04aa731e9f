package com.facishare.open.outer.oa.connector.common.api.params;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class SupportAppParams   extends  CepArg implements Serializable {

    private String outEa;

    private ChannelEnum channelEnum;


    @Data
    public static class AppInfoResult implements Serializable{
        private String appId;
        private String appName;
    }
}
