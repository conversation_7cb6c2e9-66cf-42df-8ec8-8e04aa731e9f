package com.facishare.open.outer.oa.connector.common.api.outerInterface.factory;

import com.facishare.dubbo.plugin.client.DubboRestFactoryBean;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 外部OA设置服务接口 用于实现不同应用（钉钉、企微、飞书等）的连接器配置、授权和推送功能
 *
 * @param <T> 连接器配置类型，必须继承自BaseConnectorVo
 */
@Data
public class OuterServiceDubboRestFactoryBean<T extends OuterAbstractSettingService> extends DubboRestFactoryBean<T> {
    private List<ChannelEnum> channelEnums;
    private List<OuterOaAppInfoTypeEnum> typeEnums = Arrays.stream(OuterOaAppInfoTypeEnum.values()).collect(Collectors.toList());
}
